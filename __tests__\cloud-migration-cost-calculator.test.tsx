import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CloudMigrationBody from '@components/CloudMigrationBody';
import CostFactorInput from '@components/CostFactorInput';
import CostBreakdownChart from '@components/CostBreakdownChart';
import SavingsProjection from '@components/SavingsProjection';

// Mock data
const mockBodyData = {
  hero_section: {
    title: 'Cloud Migration Cost Calculator',
    description: 'Calculate your cloud migration costs',
  },
  cost_calculator_components: {
    data: [
      {
        attributes: {
          heading: 'Infrastructure Assessment',
          description: 'Evaluate your current infrastructure',
          section_weight: 0.3,
          cost_factors: [
            {
              name: 'Server Count',
              description: 'Number of servers to migrate',
              factor_type: 'infrastructure',
              input_type: 'number_input',
              min_value: 1,
              max_value: 1000,
              unit: 'servers',
              cost_multiplier: 100,
            },
            {
              name: 'Storage Requirements',
              description: 'Total storage needed',
              factor_type: 'infrastructure',
              input_type: 'slider',
              min_value: 100,
              max_value: 10000,
              unit: 'GB',
              cost_multiplier: 0.1,
            },
          ],
        },
      },
    ],
  },
  cost_ranges: [
    {
      name: 'Small Scale',
      min_cost: 0,
      max_cost: 50000,
      color: '#28a745',
      description: 'Suitable for small businesses',
    },
    {
      name: 'Enterprise Scale',
      min_cost: 50001,
      max_cost: null,
      color: '#dc3545',
      description: 'Large enterprise migration',
    },
  ],
};

const mockFormData = {
  title: 'Get Your Cost Analysis',
  formFields: {
    fieldNameFor_FirstName: 'First Name',
    fieldNameFor_LastName: 'Last Name',
    fieldNameFor_EmailAddress: 'Email Address',
    fieldNameFor_CompanyName: 'Company Name',
    fieldNameFor_PhoneNumber: 'Phone Number',
    fieldNameFor_HowCanWeHelpYou: 'How can we help?',
  },
  consent_statement: 'I agree to the terms and conditions',
};

const mockCostResults = {
  totalMonthlyCost: 5000,
  totalOneTimeCost: 25000,
  annualCost: 60000,
  threeYearCost: 205000,
  sectionBreakdown: [
    {
      name: 'Infrastructure',
      monthlyCost: 3000,
      oneTimeCost: 15000,
    },
    {
      name: 'Licensing',
      monthlyCost: 2000,
      oneTimeCost: 10000,
    },
  ],
};

// Mock hooks and utilities
jest.mock('@hooks/useMediaQueryState', () => ({
  __esModule: true,
  default: jest.fn(() => false),
}));

jest.mock('@utils/getUserLocation', () => ({
  __esModule: true,
  default: jest.fn(() => 'US'),
}));

jest.mock('@hooks/useForm', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    values: {
      firstName: '',
      lastName: '',
      emailAddress: '',
      phoneNumber: '',
      companyName: '',
      howCanWeHelpYou: '',
      consent: false,
    },
    errors: {
      firstName: false,
      lastName: false,
      emailAddress: false,
      phoneNumber: false,
      consent: false,
    },
    errorMessages: {},
    handleChange: jest.fn(),
    handleSubmitCloudMigration: jest.fn(),
  })),
}));

describe('Cloud Migration Cost Calculator', () => {
  describe('CloudMigrationBody Component', () => {
    it('renders hero section correctly', () => {
      render(<CloudMigrationBody body={mockBodyData} formData={mockFormData} />);
      
      expect(screen.getByText('Cloud Migration Cost Calculator')).toBeInTheDocument();
    });

    it('displays cost calculator sections', () => {
      render(<CloudMigrationBody body={mockBodyData} formData={mockFormData} />);
      
      expect(screen.getByText('1. Infrastructure Assessment')).toBeInTheDocument();
      expect(screen.getByText('Evaluate your current infrastructure')).toBeInTheDocument();
    });

    it('handles navigation between sections', () => {
      render(<CloudMigrationBody body={mockBodyData} formData={mockFormData} />);
      
      // Should show first section initially
      expect(screen.getByText('1. Infrastructure Assessment')).toBeInTheDocument();
    });
  });

  describe('CostFactorInput Component', () => {
    const mockFactor = {
      name: 'Server Count',
      description: 'Number of servers to migrate',
      factor_type: 'infrastructure',
      input_type: 'number_input',
      min_value: 1,
      max_value: 1000,
      unit: 'servers',
      cost_multiplier: 100,
    };

    const mockProps = {
      factor: mockFactor,
      sectionIndex: 0,
      factorIndex: 0,
      costData: undefined,
      hasError: false,
      onCostChange: jest.fn(),
      onErrorChange: jest.fn(),
    };

    it('renders factor name and description', () => {
      render(<CostFactorInput {...mockProps} />);
      
      expect(screen.getByText('Server Count')).toBeInTheDocument();
      expect(screen.getByText('Number of servers to migrate')).toBeInTheDocument();
    });

    it('renders number input for number_input type', () => {
      render(<CostFactorInput {...mockProps} />);
      
      const input = screen.getByPlaceholderText('Enter servers');
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('type', 'number');
    });

    it('calls onCostChange when input value changes', () => {
      const onCostChange = jest.fn();
      render(<CostFactorInput {...mockProps} onCostChange={onCostChange} />);
      
      const input = screen.getByPlaceholderText('Enter servers');
      fireEvent.change(input, { target: { value: '10' } });
      
      expect(onCostChange).toHaveBeenCalledWith(
        0, 0, 'Server Count: 10 servers', 1000, 0
      );
    });

    it('shows error state when hasError is true', () => {
      render(<CostFactorInput {...mockProps} hasError={true} />);
      
      expect(screen.getByText('This field is required')).toBeInTheDocument();
    });
  });

  describe('CostBreakdownChart Component', () => {
    it('renders chart with cost data', () => {
      render(<CostBreakdownChart data={mockCostResults.sectionBreakdown} />);
      
      expect(screen.getByText('Cost Breakdown by Category')).toBeInTheDocument();
      expect(screen.getByText('Infrastructure')).toBeInTheDocument();
      expect(screen.getByText('Licensing')).toBeInTheDocument();
    });

    it('toggles between monthly and one-time costs', () => {
      render(<CostBreakdownChart data={mockCostResults.sectionBreakdown} />);
      
      const monthlyButton = screen.getByText('Monthly Costs');
      const oneTimeButton = screen.getByText('One-time Costs');
      
      expect(monthlyButton).toHaveClass('active');
      
      fireEvent.click(oneTimeButton);
      expect(oneTimeButton).toHaveClass('active');
    });
  });

  describe('SavingsProjection Component', () => {
    it('renders ROI information', () => {
      render(<SavingsProjection costResults={mockCostResults} />);
      
      expect(screen.getByText('Projected Savings & ROI')).toBeInTheDocument();
      expect(screen.getByText('Return on Investment')).toBeInTheDocument();
    });

    it('switches between timeframes', () => {
      render(<SavingsProjection costResults={mockCostResults} />);
      
      const oneYearButton = screen.getByText('1 Year');
      const threeYearButton = screen.getByText('3 Years');
      const fiveYearButton = screen.getByText('5 Years');
      
      expect(threeYearButton).toHaveClass('active');
      
      fireEvent.click(oneYearButton);
      expect(oneYearButton).toHaveClass('active');
    });
  });

  describe('Cost Calculation Logic', () => {
    it('calculates total costs correctly', () => {
      // Mock cost data
      const costData = [
        [
          ['Server Count: 10 servers', 1000, 5000],
          ['Storage: 500 GB', 50, 0],
        ],
      ];

      const sectionWeight = 0.3;
      
      // Calculate expected results
      const expectedMonthlyCost = (1000 + 50) * sectionWeight;
      const expectedOneTimeCost = 5000 * sectionWeight;
      
      expect(expectedMonthlyCost).toBe(315);
      expect(expectedOneTimeCost).toBe(1500);
    });

    it('determines correct cost range', () => {
      const totalCost = 30000;
      const ranges = mockBodyData.cost_ranges;
      
      const range = ranges.find(r => 
        totalCost >= r.min_cost && (!r.max_cost || totalCost <= r.max_cost)
      );
      
      expect(range?.name).toBe('Small Scale');
    });
  });

  describe('Form Validation', () => {
    it('validates required fields', () => {
      const formData = {
        firstName: '',
        lastName: 'Doe',
        emailAddress: '<EMAIL>',
        phoneNumber: '1234567890',
        consent: true,
      };

      const hasErrors = !formData.firstName || !formData.lastName || 
                       !formData.emailAddress || !formData.phoneNumber || 
                       !formData.consent;

      expect(hasErrors).toBe(true);
    });

    it('validates email format', () => {
      const validEmail = '<EMAIL>';
      const invalidEmail = 'invalid-email';
      
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      
      expect(emailRegex.test(validEmail)).toBe(true);
      expect(emailRegex.test(invalidEmail)).toBe(false);
    });
  });

  describe('API Integration', () => {
    beforeEach(() => {
      global.fetch = jest.fn();
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('submits form data to API endpoint', async () => {
      const mockResponse = { ok: true, json: () => Promise.resolve({ success: true }) };
      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const formData = {
        firstName: 'John',
        lastName: 'Doe',
        emailAddress: '<EMAIL>',
        totalMonthlyCost: 5000,
        totalOneTimeCost: 25000,
      };

      const response = await fetch('/api/cloud-migration-cost-calculator', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      expect(fetch).toHaveBeenCalledWith('/api/cloud-migration-cost-calculator', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      expect(response.ok).toBe(true);
    });

    it('handles API errors gracefully', async () => {
      const mockResponse = { 
        ok: false, 
        status: 500,
        json: () => Promise.resolve({ error: 'Internal server error' }) 
      };
      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const response = await fetch('/api/cloud-migration-cost-calculator', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}),
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(500);
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(<CloudMigrationBody body={mockBodyData} formData={mockFormData} />);
      
      // Check for proper heading structure
      const headings = screen.getAllByRole('heading');
      expect(headings.length).toBeGreaterThan(0);
    });

    it('supports keyboard navigation', () => {
      const mockFactor = {
        name: 'Test Factor',
        input_type: 'number_input',
        cost_multiplier: 1,
      };

      render(
        <CostFactorInput
          factor={mockFactor}
          sectionIndex={0}
          factorIndex={0}
          costData={undefined}
          hasError={false}
          onCostChange={jest.fn()}
          onErrorChange={jest.fn()}
        />
      );

      const input = screen.getByRole('spinbutton');
      expect(input).toBeInTheDocument();
      
      // Test focus
      input.focus();
      expect(input).toHaveFocus();
    });
  });
});
