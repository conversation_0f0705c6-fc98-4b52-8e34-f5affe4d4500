import { NextResponse } from 'next/server';
import sendDataToHubspot from 'common/sendDataToHubSpot';
import sendDataToSendGrid from 'common/sendDataToSendGrid';
import currentTimestamp from 'common/currentTimestamp';
import sendToSlack from 'common/sendDataToSlack';

export async function POST(req: Request) {
  try {
    const form_data = await req.json();

    const formFields = [
      { name: 'firstname', value: form_data?.firstName ?? '' },
      { name: 'lastname', value: form_data?.lastName ?? '' },
      { name: 'email', value: form_data?.emailAddress ?? '' },
      { name: 'company', value: form_data?.companyName ?? '' },
      { name: 'phone', value: form_data?.phoneNumber ?? '' },
      { name: 'city', value: form_data?.city ?? '' },
      { name: 'country', value: form_data?.country ?? '' },
      { name: 'ip_address', value: form_data?.ip_address ?? '' },
      { name: 'ga_4_userid', value: form_data?.ga_4_userid ?? '' },
      { name: 'clarity_link', value: form_data?.clarity ?? '' },
      { name: 'source', value: form_data?.secondary_source ?? 'CloudMigrationCostCalculator' },
      { name: 'source_url', value: form_data?.url ?? '' },
      { name: 'utm_campaign', value: form_data?.utm_campaign ?? '' },
      { name: 'utm_source', value: form_data?.utm_source ?? '' },
      { name: 'utm_medium', value: form_data?.utm_medium ?? '' },
      { name: 'referrer', value: form_data?.referrer ?? '' },
      { name: 'consent', value: form_data?.consent ?? '' },
      { name: 'how_can_we_help_you', value: form_data?.howCanWeHelpYou ?? '' },
      
      // Cost calculation results
      { name: 'total_monthly_cost', value: form_data?.totalMonthlyCost ?? '' },
      { name: 'total_one_time_cost', value: form_data?.totalOneTimeCost ?? '' },
      { name: 'annual_cost', value: form_data?.annualCost ?? '' },
      { name: 'three_year_cost', value: form_data?.threeYearCost ?? '' },
      { name: 'cost_range_category', value: form_data?.costRangeCategory ?? '' },
      
      // Infrastructure costs
      { name: 'infrastructure_monthly_cost', value: form_data?.infrastructureMonthlyCost ?? '' },
      { name: 'infrastructure_one_time_cost', value: form_data?.infrastructureOneTimeCost ?? '' },
      
      // Licensing costs
      { name: 'licensing_monthly_cost', value: form_data?.licensingMonthlyCost ?? '' },
      { name: 'licensing_one_time_cost', value: form_data?.licensingOneTimeCost ?? '' },
      
      // Migration costs
      { name: 'migration_monthly_cost', value: form_data?.migrationMonthlyCost ?? '' },
      { name: 'migration_one_time_cost', value: form_data?.migrationOneTimeCost ?? '' },
      
      // Training costs
      { name: 'training_monthly_cost', value: form_data?.trainingMonthlyCost ?? '' },
      { name: 'training_one_time_cost', value: form_data?.trainingOneTimeCost ?? '' },
      
      // Maintenance costs
      { name: 'maintenance_monthly_cost', value: form_data?.maintenanceMonthlyCost ?? '' },
      { name: 'maintenance_one_time_cost', value: form_data?.maintenanceOneTimeCost ?? '' },
      
      // Detailed cost factors (store as JSON string)
      { name: 'detailed_cost_breakdown', value: JSON.stringify(form_data?.detailedCostBreakdown ?? {}) },
      
      // ROI projections
      { name: 'projected_annual_savings', value: form_data?.projectedAnnualSavings ?? '' },
      { name: 'three_year_roi', value: form_data?.threeYearROI ?? '' },
      { name: 'payback_period_months', value: form_data?.paybackPeriodMonths ?? '' },
    ];

    const payload = {
      fields: formFields,
      context: { pageUri: form_data?.url },
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_HUBSPOT_API_KEY}`,
      },
    };

    try {
      // Send Data to HubSpot
      const hubspotResponse = await sendDataToHubspot(
        form_data?.secondary_source,
        payload,
        process.env.NEXT_PUBLIC_HUBSPOT_CLOUD_MIGRATION_FORM_GUID,
      );

      if (hubspotResponse?.status === 200 || hubspotResponse?.status === 201) {
        // Send Data to SendGrid if HubSpot submission is successful
        const emailRes = await sendDataToSendGrid(
          process.env.NEXT_PUBLIC_MAIL_TO,
          process.env.NEXT_PUBLIC_MAIL_FROM,
          form_data?.emailAddress,
          process.env.NEXT_PUBLIC_SENDGRID_CLOUD_MIGRATION_TEMPLATE_ID,
          form_data,
        );

        // Send Data to success slack channel
        await sendToSlack(
          form_data,
          process.env.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL,
        );

        // NECESSARY LOGS -> FOR DEBUGGING PURPOSE
        console.log(currentTimestamp());
        console.log('Cloud Migration Cost Calculator Lead Data', form_data);
        console.log('HubSpot Response', hubspotResponse);
        console.log('SendGrid Email Response', emailRes);

        return NextResponse.json(
          {
            message: 'Cost calculation and form submitted successfully.',
            hubspotResponse: hubspotResponse.message,
          },
          { status: 200 },
        );
      } else {
        console.error('HubSpot Error:', hubspotResponse);

        // If HubSpot POST API fails -> send failure email
        let formLeadData = form_data;
        formLeadData.page_name = form_data?.secondary_source;
        formLeadData.failed_source = 'Hubspot';

        const failureEmail = await sendDataToSendGrid(
          process.env.NEXT_PUBLIC_MAIL_TO,
          process.env.NEXT_PUBLIC_MAIL_FROM,
          form_data?.emailAddress,
          process.env.NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID,
          formLeadData,
        );

        // If HubSpot submission fails -> Send to a failure slack channel
        await sendToSlack(
          form_data,
          process.env.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL,
          '⚠️ HubSpot Cloud Migration Form Submission Failed ⚠️',
        );

        console.log('Failure Email Response', failureEmail);

        return NextResponse.json(
          {
            message: 'Error submitting to HubSpot, but backup processes completed.',
            error: hubspotResponse.error,
          },
          { status: 500 },
        );
      }
    } catch (integrationError) {
      console.error('Integration Error:', integrationError);

      // Send to failure slack channel for integration errors
      await sendToSlack(
        form_data,
        process.env.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL,
        '🚨 Cloud Migration Cost Calculator Integration Error 🚨',
      );

      return NextResponse.json(
        {
          message: 'Error in external integrations.',
          error: integrationError.message,
        },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('API Route Error:', error);

    return NextResponse.json(
      {
        message: 'Internal server error.',
        error: error.message,
      },
      { status: 500 },
    );
  }
}
