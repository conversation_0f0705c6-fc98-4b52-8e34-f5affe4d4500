import { NextResponse } from 'next/server';
import { generateAllSitemapUrls, generateXmlSitemap } from '@utils/sitemapUtils';

export const dynamic = 'force-dynamic';

/**
 * GET /api/sitemap
 * Generates and returns the XML sitemap
 */
export async function GET() {
  try {
    // Generate all sitemap URLs
    const urls = await generateAllSitemapUrls();
    
    // Generate XML sitemap
    const xmlSitemap = generateXmlSitemap(urls);
    
    // Return XML response with proper headers
    return new NextResponse(xmlSitemap, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    });
  } catch (error) {
    console.error('Error generating sitemap:', error);
    
    return NextResponse.json(
      { error: 'Failed to generate sitemap' },
      { status: 500 }
    );
  }
}
