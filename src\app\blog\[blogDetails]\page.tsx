import React from 'react';
import BlogDetailsWrapper from '@components/Wrappers/BlogDetailsWrappers';
import seoSchema from '@utils/seoSchema';
import { notFound } from 'next/navigation';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

export const dynamic = 'force-static';

export async function generateStaticParams() {
  const blogPathResponse = await fetchFromStrapi('blogs');

  const blogDetails = blogPathResponse.data.map(res => ({
    blogDetails: res.attributes.slug,
  }));

  return blogDetails;
}

export async function fetchBlogData(slug: string) {
  const queryString = `filters[slug][$eq]=${slug}&populate=content&populate=heroSection_image&populate=suggestions.blogs&populate=suggestions.blogs.content&populate=suggestions.ebooks&populate=suggestions.case_studies&populate=suggestions.white_papers&populate=caseStudy_suggestions.case_study&populate=authors.image&populate=seo&populate=seo.image&populate=content&populate=suggestions.blogs.image&populate=suggestions.blogs.authors.image&populate=suggestions.ebooks.image&populate=suggestions.ebooks.authors&populate=suggestions.case_studies.image&populate=suggestions.case_studies.authors&populate=suggestions.white_papers.image&populate=suggestions.white_papers.authors&populate=caseStudy_suggestions.cover_image&populate=authors.image&populate=image&populate=authors.image&populate=suggestions.ebooks.authors.image&populate=suggestions.case_studies.authors.image&populate=suggestions.white_papers.authors.image&populate=blog_related_service,seo.schema`;

  return await fetchFromStrapi('blogs', queryString);
}

export async function generateMetadata({
  params,
}: {
  params: { blogDetails: string };
}) {
  const { blogDetails: slug } = params;

  const queryString = `filters[slug][$eq]=${slug}&populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema`;

  const seoFetchedData = await fetchFromStrapi('blogs', queryString);

  const seoData = seoFetchedData?.data[0]?.attributes?.seo;

  return seoSchema(seoData);
}

export default async function Blog({
  params,
}: {
  params: { blogDetails: string };
}) {
  const { blogDetails } = params;
  const blogData = await fetchBlogData(blogDetails);

  // Check if blog page data exists, otherwise return 404
  if (!blogData?.data || blogData?.data.length === 0) {
    notFound();
  }
  return (
    <>
      {blogData?.data[0]?.attributes?.seo && (
        <RichResults data={blogData?.data[0]?.attributes?.seo} />
      )}
      <BlogDetailsWrapper blogData={blogData} />
    </>
  );
}
