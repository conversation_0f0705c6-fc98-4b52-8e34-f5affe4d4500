import React from 'react';
import CaseStudyHeroSection from '@components/CaseStudyHeroSection';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import BlogListing from '@components/BlogListing';
import seoSchema from '@utils/seoSchema';
import RichResults from '@components/RichResults';

export async function fetchBlogListingPageData() {
  return await fetchFromStrapi(
    'blog-listing-page',
    'populate=hero_section.image,filter,seo.schema',
  );
}

export async function fetchBlogListingPageDropDownServiceData() {
  return await fetchFromStrapi('global-services');
}

export async function fetchBlogListingPageDropDownIndustryData() {
  return await fetchFromStrapi('global-industries');
}

export async function fetchBlogListingBoxData() {
  const response = await fetchFromStrapi(
    'blogs',
    'populate=heroSection_image,global_industries,global_services&sort=createdAt:desc',
  );

  return response?.data || [];
}

export async function generateMetadata() {
  const seoFetchedData = await fetchFromStrapi(
    'blog-listing-page',
    'populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema',
  );

  const seoData = seoFetchedData?.data?.attributes?.seo;

  return seoSchema(seoData);
}

export default async function BlogListingPage() {
  const blogListingData = await fetchBlogListingPageData();
  const blogListingBoxData = await fetchBlogListingBoxData();
  const blogListingPageDropDownServiceData =
    await fetchBlogListingPageDropDownServiceData();
  const blogListingPageDropDownIndustryData =
    await fetchBlogListingPageDropDownIndustryData();
  return (
    <>
      {blogListingData?.data?.attributes?.seo && (
        <RichResults data={blogListingData?.data?.attributes?.seo} />
      )}
      <CaseStudyHeroSection
        heroData={blogListingData?.data?.attributes?.hero_section}
        variant={'listing_page'}
      />

      <BlogListing
        filterdata={blogListingData?.data?.attributes?.filter}
        boxData={blogListingBoxData}
        dropDownServicesData={blogListingPageDropDownServiceData?.data}
        dropDownIndustryData={blogListingPageDropDownIndustryData?.data}
      />
    </>
  );
}
