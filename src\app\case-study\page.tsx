import React from 'react';
import CaseStudyHeroSection from '@components/CaseStudyHeroSection';
import CaseStudyListing from '@components/CaseStudyListing';
import seoSchema from '@utils/seoSchema';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

export async function fetchCaseStudyListingPageData() {
  const queryString = `populate=hero_section.image,filter,seo.schema`;
  return await fetchFromStrapi('case-study-listing-page', queryString);
}

export async function fetchCaseStudyListingPageDropDownServiceData() {
  return await fetchFromStrapi('global-services');
}

export async function fetchCaseStudyListingPageDropDownIndustryData() {
  return await fetchFromStrapi('global-industries');
}

export async function fetchCaseStudyListingBoxData() {
  const queryString = `sort=id:desc&populate=hero_section.global_industries,hero_section.global_services,hero_section.image`;
  return await fetchFromStrapi('case-studies', queryString);
}

export async function generateMetadata() {
  const queryString = `populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema`;
  const seoFetchedData = await fetchFromStrapi(
    'case-study-listing-page',
    queryString,
  );
  const seoData = seoFetchedData?.data?.attributes?.seo;
  return seoSchema(seoData);
}
export default async function CaseStudyListingPage() {
  const caseStudyListingData = await fetchCaseStudyListingPageData();
  const caseStudyListingBoxData = await fetchCaseStudyListingBoxData();
  const caseStudyListingPageDropDownServiceData =
    await fetchCaseStudyListingPageDropDownServiceData();
  const caseStudyListingPageDropDownIndustryData =
    await fetchCaseStudyListingPageDropDownIndustryData();
  return (
    <>
      {caseStudyListingData?.data?.attributes?.seo && (
        <RichResults data={caseStudyListingData?.data?.attributes?.seo} />
      )}
      <CaseStudyHeroSection
        heroData={caseStudyListingData?.data?.attributes?.hero_section}
        variant={'listing_page'}
      />
      <CaseStudyListing
        filterdata={caseStudyListingData?.data?.attributes?.filter}
        boxData={caseStudyListingBoxData?.data}
        dropDownServicesData={caseStudyListingPageDropDownServiceData?.data}
        dropDownIndustryData={caseStudyListingPageDropDownIndustryData?.data}
      />
    </>
  );
}
