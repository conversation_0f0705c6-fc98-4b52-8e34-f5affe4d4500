import { notFound } from 'next/navigation';
import CloudMigrationBody from '@components/CloudMigrationBody';
import seoSchema from '@utils/seoSchema';

import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

async function getCloudMigrationData() {
  return await fetchFromStrapi(
    'cloud-migration-cost-calculator',
    'populate=hero_section.image,cost_calculator_components.cost_factors.cost_options,form.formFields,form.button,restart_button,consultation_button,cost_ranges,seo.schema',
  );
}

async function getFormData() {
  const query = `populate=form.formFields&populate=form.button`;
  return await fetchFromStrapi('form', query);
}

export async function generateMetadata({}) {
  const seoFetchedData = await fetchFromStrapi(
    'cloud-migration-cost-calculator',
    'populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema',
  );
  const seoData = seoFetchedData?.data?.attributes?.seo;

  return seoSchema(seoData);
}

export default async function CloudMigrationCostCalculator() {
  const cloudMigrationData = await getCloudMigrationData();
  const formData = await getFormData();

  if (!cloudMigrationData?.data || cloudMigrationData?.data.length === 0) {
    notFound();
  }

  return (
    <>
      {cloudMigrationData?.data?.attributes?.seo && (
        <RichResults data={cloudMigrationData?.data?.attributes?.seo} />
      )}
      {cloudMigrationData?.data?.attributes && (
        <CloudMigrationBody
          body={cloudMigrationData?.data?.attributes}
          formData={formData?.data?.attributes?.form}
        />
      )}
    </>
  );
}
