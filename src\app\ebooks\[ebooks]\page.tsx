import React from 'react';
import seoSchema from '@utils/seoSchema';
import { notFound } from 'next/navigation';
import RichText from '@components/RichText';
import CaseStudyForm from '@components/CaseStudyForm';
import EbooksHeroSection from '@components/EbooksHeroSection';
import RichResults from '@components/RichResults';
import fetchFromStrapi from '@utils/fetchFromStrapi';

export async function generateStaticParams() {
  const ebooksResponse = await fetchFromStrapi('e-books', '');
  const ebooks = ebooksResponse.data.map(res => ({
    ebooks: res.attributes.slug,
  }));
  return ebooks;
}

export async function fetchEbooksData(slug: string) {
  const queryString = `filters[slug][$eq]=${slug}&populate=global_resource_type,preview.preview_image,hero_section.image,form.form_values,form.form_download_button,seo.schema`;
  return await fetchFromStrapi('e-books', queryString);
}

export async function generateMetadata({
  params,
}: {
  params: { ebooks: string };
}) {
  const { ebooks: slug } = params;
  const queryString = `filters[slug][$eq]=${slug}&populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema`;
  const seoFetchedData = await fetchFromStrapi('e-books', queryString);
  const seoData = seoFetchedData?.data[0]?.attributes?.seo;
  return seoSchema(seoData);
}
export default async function EbooksDetails({ params }: { params: any }) {
  const { ebooks } = params;
  const ebooksData = await fetchEbooksData(ebooks);

  // Check if case-study page data exists, otherwise return 404
  if (!ebooksData?.data || ebooksData?.data.length === 0) {
    notFound();
  }

  return (
    <>
      {ebooksData?.data[0]?.attributes?.seo && (
        <RichResults data={ebooksData?.data[0]?.attributes?.seo} />
      )}
      {ebooksData?.data[0]?.attributes?.hero_section && (
        <EbooksHeroSection
          heroData={ebooksData?.data[0]?.attributes?.hero_section}
        />
      )}
      {ebooksData?.data[0]?.attributes?.rich_text && (
        <RichText richTextData={ebooksData?.data[0]?.attributes} />
      )}
      {ebooksData?.data[0]?.attributes?.form && (
        <CaseStudyForm
          formData={ebooksData?.data[0]?.attributes?.form}
          source="eBooks"
        />
      )}
    </>
  );
}
