import React from 'react';
import ContactUsForm from '@components/ContactUsForm';
import EventsListing from '@components/EventsListing';
import HeroSection from '@components/HeroSection';
import seoSchema from '@utils/seoSchema';
import RichResults from '@components/RichResults';
import fetchFromStrapi from '@utils/fetchFromStrapi';

export async function fetchEventsListingPageData() {
  const queryString =
    'populate=hero_section.image,form.formFields,form.button,seo.schema';
  return await fetchFromStrapi('event-listing-page', queryString);
}

export async function fetchEventsListingBoxData() {
  const queryString = 'populate=hero_section.hero_image';
  return await fetchFromStrapi('event-main-pages', queryString);
}

async function getFormData() {
  const queryString = 'populate=form.formFields&populate=form.button';
  return await fetchFromStrapi('form', queryString);
}

export async function generateMetadata({}) {
  const queryString =
    'populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema';
  const seoFetchedData = await fetchFromStrapi(
    'event-listing-page',
    queryString,
  );
  const seoData = seoFetchedData?.data?.attributes?.seo;
  return seoSchema(seoData);
}

export default async function EventsListingPage() {
  const eventsListingData = await fetchEventsListingPageData();
  const eventsListingBoxData = await fetchEventsListingBoxData();
  const formData = await getFormData();

  return (
    <>
      {eventsListingData?.data?.attributes?.seo && (
        <RichResults data={eventsListingData?.data?.attributes?.seo} />
      )}
      {eventsListingData?.data?.attributes?.hero_section && (
        <HeroSection
          heroData={eventsListingData?.data?.attributes?.hero_section}
          variant="primary"
        />
      )}
      <EventsListing eventsListingBoxData={eventsListingBoxData?.data} />
      {formData?.data?.attributes?.form && (
        <ContactUsForm
          formData={formData?.data?.attributes?.form}
          source="Events"
        />
      )}
    </>
  );
}
