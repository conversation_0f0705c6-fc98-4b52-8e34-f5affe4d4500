import React from 'react';
import { Metadata } from 'next';
import { Poppins } from 'next/font/google';
import dynamic from 'next/dynamic';
import BootstrapClient from '@components/BootstrapClient';
import Header from '@components/Header';
import 'bootstrap/dist/css/bootstrap.css';
import '@styles/base.css';
import GTM from '@components/GTM';
import fetchFromStrapi from '@utils/fetchFromStrapi';

const CookieConsentBannerDynamic = dynamic(
  () => import('@components/CookiesConsentBanner'),
  { ssr: false },
);

const CircularButtonWithArrowDynamic = dynamic(
  () => import('@components/CircularButtonWithArrow'),
);

const FooterDynamic = dynamic(() => import('@components/Footer'));

const poppins = Poppins({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

export const metadata: Metadata = {
  icons: {
    icon: 'https://cdn.marutitech.com/marutitech_logo_fe5e4e6162.svg',
  },
  verification: {
    google: `${process.env.NEXT_PUBLIC__GOOGLE_SITE_VERIFICATION}`,
  },
};

async function getHeaderData() {
  const query = `populate=logo&populate=logo.image&populate=menu.subMenu&populate=menu.subMenu.image&populate=menu.subLinks&populate=menu.button&populate=menu.titleDescription`;
  return await fetchFromStrapi('header', query);
}

async function fetchFooterData() {
  const query = `populate=sector_row.Sublinks,pages_row.Sublinks,terms_and_condition_section,company_logo_section.image,company_logo_section.Copyright,company_logo_section.social_platforms.image`;
  return await fetchFromStrapi('footer', query);
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const headerData = getHeaderData();
  const footerData = fetchFooterData();
  const [header, footer] = await Promise.all([headerData, footerData]);

  return (
    <html lang="en">
      <head>
        <GTM />
        <link rel="preconnect" href="https://api.ipify.org" />
        <link rel="preconnect" href="https://ipwhois.app" />
      </head>
      <body className={poppins.className}>
        <CookieConsentBannerDynamic />
        <Header headerData={header} />
        {children}
        <FooterDynamic footerData={footer} />
        <div id="scroll_to_top">
          <CircularButtonWithArrowDynamic variant="scroll_to_top" scroll_to />
        </div>
        <BootstrapClient />
      </body>
    </html>
  );
}
