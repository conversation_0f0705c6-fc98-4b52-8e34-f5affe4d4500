import HeroSection from '@components/HeroSection';
import HomeHeroSection from '@components/HomeHeroSection';
import React from 'react';
import AllResourcesFilter from '@components/AllResourcesFilter';
import seoSchema from '@utils/seoSchema';
import RichResults from '@components/RichResults';
import fetchFromStrapi from '@utils/fetchFromStrapi';

export async function fetchResourcesPageData() {
  const queryString =
    'populate=hero_section.image,resources_silder,filter_ui,seo.schema';
  return await fetchFromStrapi('all-resources-page', queryString);
}

export async function fetchCaseStudyListingPageDropDownServiceData() {
  return await fetchFromStrapi('global-services');
}

export async function fetchCaseStudyListingPageDropDownIndustryData() {
  return await fetchFromStrapi('global-industries');
}

export async function fetchAllResourcesPageDropDownServiceData() {
  return await fetchFromStrapi('global-resource-types');
}

export async function generateMetadata({}) {
  const queryString =
    'populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema';
  const seoFetchedData = await fetchFromStrapi(
    'all-resources-page',
    queryString,
  );
  const seoData = seoFetchedData?.data?.attributes?.seo;

  // Return the metadata
  return seoSchema(seoData);
}

export async function fetchCombinedListingData() {
  try {
    const [caseStudyData, eventsData, eBooksData, whitePapersData, blogData] =
      await Promise.all([
        fetch(
          `${process.env.NEXT_PUBLIC_STRAPI_URL}/api/case-studies?populate=hero_section.global_industries,hero_section.global_services,hero_section.image,global_resource_type&sort=createdAt:desc`,
        ).then(res => res.json()),
        fetch(
          `${process.env.NEXT_PUBLIC_STRAPI_URL}/api/event-main-pages?populate=hero_section.hero_image,global_resource_type&sort=createdAt:desc`,
        ).then(res => res.json()),
        fetch(
          `${process.env.NEXT_PUBLIC_STRAPI_URL}/api/e-books?populate=preview.preview_image,global_resource_type&sort=createdAt:desc`,
        ).then(res => res.json()),
        fetch(
          `${process.env.NEXT_PUBLIC_STRAPI_URL}/api/white-papers?populate=preview.preview_image,global_resource_type&sort=createdAt:desc`,
        ).then(res => res.json()),
        fetch(
          `${process.env.NEXT_PUBLIC_STRAPI_URL}/api/blogs?populate=heroSection_image,global_industries,global_services,global_resource_type&sort=createdAt:desc`,
        ).then(res => res.json()),
      ]);

    // Normalize the case studies data
    const caseStudyItems =
      caseStudyData?.data?.map(item => ({
        id: item.id,
        type: 'case-study',
        attributes: {
          ...item.attributes,
          hero_section: {
            ...item.attributes.hero_section,
            global_industries: item.attributes.hero_section
              .global_industries || { data: [] },
            global_services: item.attributes.hero_section.global_services || {
              data: [],
            },
            global_resource_type: item.attributes.hero_section
              .global_resource_type || { data: [] },
          },
        },
      })) || [];

    // Normalize the events data
    const eventItems =
      eventsData?.data?.map(item => ({
        id: item.id,
        type: 'event',
        attributes: {
          ...item.attributes,
          hero_section: {
            ...item.attributes.hero_section,
            global_industries: { data: [] }, // Add missing property with default value
            global_services: { data: [] }, // Add missing property with default value
          },
          global_resource_type: item.attributes.global_resource_type || {
            data: [],
          },
        },
      })) || [];

    const eBooksItems =
      eBooksData?.data?.map(item => ({
        id: item.id,
        type: 'ebook',
        attributes: {
          ...item.attributes,
          hero_section: {
            global_industries: { data: [] },
            global_services: { data: [] },
          },
          global_resource_type: item.attributes.global_resource_type || {
            data: [],
          },
        },
      })) || [];

    const whitePapersItems =
      whitePapersData?.data?.map(item => ({
        id: item.id,
        type: 'white-paper',
        attributes: {
          ...item.attributes,
          hero_section: {
            global_industries: { data: [] },
            global_services: { data: [] },
          },
          global_resource_type: item.attributes.global_resource_type || {
            data: [],
          },
        },
      })) || [];

    // Normalize the blogs data
    const blogItems =
      blogData?.data?.map(item => ({
        id: item.id,
        type: 'blog',
        attributes: {
          ...item.attributes,
          hero_section: {
            global_industries: item.attributes.global_industries || {
              data: [],
            },
            global_services: item.attributes.global_services || { data: [] },
          },
          global_resource_type: { data: ['Blog'] },
        },
      })) || [];

    // Combine all datasets
    const combinedData = [
      ...caseStudyItems,
      ...eventItems,
      ...eBooksItems,
      ...whitePapersItems,
      ...blogItems,
    ];

    return combinedData;
  } catch (error) {
    console.error('Error fetching combined data:', error);
    throw error;
  }
}

export default async function AllResourcesPage() {
  const allResourcesPageData = await fetchResourcesPageData();
  const allResourcesPageDropDownServiceData =
    await fetchCaseStudyListingPageDropDownServiceData();
  const allResourcesPageDropDownIndustryData =
    await fetchCaseStudyListingPageDropDownIndustryData();

  const allResourcesPageDropDownResourceTypeData =
    await fetchAllResourcesPageDropDownServiceData();

  const boxData = await fetchCombinedListingData();

  return (
    <>
      {allResourcesPageData?.data?.attributes?.seo && (
        <RichResults data={allResourcesPageData?.data?.attributes?.seo} />
      )}
      {allResourcesPageData?.data?.attributes?.hero_section && (
        <HeroSection
          heroData={allResourcesPageData?.data?.attributes?.hero_section}
          variant="primary"
          resources_page={true}
        />
      )}

      {allResourcesPageData?.data?.attributes?.resources_silder && (
        <HomeHeroSection
          resourcesSlide={
            allResourcesPageData?.data?.attributes?.resources_silder
          }
          varaint="resources"
        />
      )}

      <AllResourcesFilter
        filterdata={allResourcesPageData?.data?.attributes?.filter_ui}
        dropDownServicesData={allResourcesPageDropDownServiceData?.data}
        dropDownIndustryData={allResourcesPageDropDownIndustryData?.data}
        dropDownResourceTypeData={
          allResourcesPageDropDownResourceTypeData?.data
        }
        boxData={boxData}
      />
    </>
  );
}
