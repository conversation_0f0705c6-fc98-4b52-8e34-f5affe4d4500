import ContactUsForm from '@components/ContactUsForm';
import RichText from '@components/RichText';
import AllServicePage from '@components/AllServicePage';
import RichResults from '@components/RichResults';
import seoSchema from '@utils/seoSchema';
import fetchFromStrapi from '@utils/fetchFromStrapi';

export async function fetchAllServicesPageData() {
  const query = `populate=richtext_with_title,form.formFields,form.button,all_service_page.image,all_service_page.button,all_service_page.l_2_service_pages,seo.schema`;
  return await fetchFromStrapi('all-service-page', query);
}

export async function getFormData() {
  const query = `populate=form.formFields&populate=form.button`;
  return await fetchFromStrapi('form', query);
}

export async function generateMetadata() {
  const query = `populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema`;
  const seoFetchedData = await fetchFromStrapi('all-service-page', query);

  const seoData = seoFetchedData?.data?.attributes?.seo;
  return seoSchema(seoData);
}

export default async function AllServicesPage() {
  const allServicesPageData = await fetchAllServicesPageData();
  const formData = await getFormData();

  return (
    <>
      {allServicesPageData?.data?.attributes?.seo && (
        <RichResults data={allServicesPageData?.data?.attributes?.seo} />
      )}
      {allServicesPageData?.data?.attributes?.richtext_with_title && (
        <RichText
          variant="allServicesPage"
          richTextData={
            allServicesPageData?.data?.attributes?.richtext_with_title
          }
        />
      )}

      {allServicesPageData?.data?.attributes?.all_service_page && (
        <AllServicePage
          allServicePageData={
            allServicesPageData?.data?.attributes?.all_service_page
          }
        />
      )}

      {formData?.data?.attributes?.form && (
        <ContactUsForm
          formData={formData?.data?.attributes?.form}
          source="AllServices"
        />
      )}
    </>
  );
}
