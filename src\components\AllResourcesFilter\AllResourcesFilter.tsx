'use client';
import { Container } from 'react-bootstrap';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';
import styles from './AllResourcesFilter.module.css';
import Link from 'next/link';
import Button from '@components/Button';
import Heading from '@components/Heading';
import useMediaQueryState from '@hooks/useMediaQueryState';
import breakpoints from '@styles/breakpoints.module.css';
import React from 'react';
import { formatPublishDate } from '@utils/dateFormatter';
import ImageWithSizing from '@components/ImageWithSizing';

export default function AllResourcesFilter({
  filterdata,
  boxData,
  dropDownServicesData,
  dropDownIndustryData,
  dropDownResourceTypeData,
}: any) {
  // Manage which dropdown is open (null, 'services', or 'industry')
  const [openDropdown, setOpenDropdown] = useState<
    'services' | 'industry' | 'resource_type' | null
  >(null);
  // States to store selected services and industries
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([]);
  const [selectedResourceType, setSelectedResourceType] = useState<string[]>(
    [],
  );
  const [visibleCount, setVisibleCount] = useState(9);

  const handleViewMore = () => {
    setVisibleCount(prevCount => prevCount + 9);
  };
  const filteredData = boxData?.filter(data => {
    const serviceTitles =
      data?.attributes?.hero_section?.global_services?.data?.map(
        service => service?.attributes?.service_title,
      );
    const industryTitles =
      data?.attributes?.hero_section?.global_industries?.data?.map(
        industry => industry?.attributes?.industry_title,
      );

    const resourceTypeTitles =
      data?.attributes?.global_resource_type?.data?.[0] === 'Blog'
        ? ['Blog']
        : data?.attributes?.global_resource_type?.data
          ? [
              data.attributes.global_resource_type.data.attributes
                .resource_type_title,
            ]
          : [];

    const hasMatchingService =
      selectedServices.length === 0 ||
      selectedServices.some(selected => serviceTitles?.includes(selected));

    const hasMatchingIndustry =
      selectedIndustries.length === 0 ||
      selectedIndustries.some(selected => industryTitles?.includes(selected));

    const hasMatchingResourceType =
      selectedResourceType.length === 0 ||
      selectedResourceType.some(selected =>
        resourceTypeTitles?.includes(selected),
      );

    return hasMatchingService && hasMatchingIndustry && hasMatchingResourceType;
  });

  const combinedSelectedValues = [
    ...selectedServices,
    ...selectedIndustries,
    ...selectedResourceType,
  ];

  // Function to remove selected value
  const removeSelectedValue = value => {
    setSelectedServices(selectedServices.filter(service => service !== value));
    setSelectedIndustries(
      selectedIndustries.filter(industry => industry !== value),
    );
    setSelectedResourceType(
      selectedResourceType.filter(resourceType => resourceType !== value),
    );
  };

  const toggleDropdownServices = () => {
    setOpenDropdown(prev => (prev === 'services' ? null : 'services'));
  };

  const toggleDropdownIndustry = () => {
    setOpenDropdown(prev => (prev === 'industry' ? null : 'industry'));
  };

  const toggleDropdownResourceType = () => {
    setOpenDropdown(prev =>
      prev === 'resource_type' ? null : 'resource_type',
    );
  };

  // Handler for selecting/unselecting services
  const handleServiceChange = (serviceTitle: string) => {
    setSelectedServices(prevSelectedServices => {
      if (prevSelectedServices.includes(serviceTitle)) {
        return prevSelectedServices.filter(service => service !== serviceTitle);
      } else {
        return [...prevSelectedServices, serviceTitle];
      }
    });
  };

  // Handler for selecting/unselecting industries
  const handleIndustryChange = (industryTitle: string) => {
    setSelectedIndustries(prevSelectedIndustries => {
      if (prevSelectedIndustries.includes(industryTitle)) {
        return prevSelectedIndustries.filter(
          industry => industry !== industryTitle,
        );
      } else {
        return [...prevSelectedIndustries, industryTitle];
      }
    });
  };
  // Handler for selecting/unselecting resource type
  const handleResourceTypeChange = (resourceTypeTitle: string) => {
    setSelectedResourceType(prevSelectedResourceType => {
      if (prevSelectedResourceType.includes(resourceTypeTitle)) {
        return prevSelectedResourceType.filter(
          resourceType => resourceType !== resourceTypeTitle,
        );
      } else {
        return [...prevSelectedResourceType, resourceTypeTitle];
      }
    });
  };

  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-md']})`,
  });

  // State to control the visibility of the filter container
  const [isFilterVisible, setIsFilterVisible] = useState(false);

  // Function to toggle visibility
  const toggleFilterVisibility = () => {
    setIsFilterVisible(!isFilterVisible); // Toggle visibility
  };
  // Dropdown refs
  const servicesDropdownRef = useRef<HTMLDivElement>(null);
  const industryDropdownRef = useRef<HTMLDivElement>(null);
  const resourceTypeDropdownRef = useRef<HTMLDivElement>(null);
  // Close dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        servicesDropdownRef.current &&
        !servicesDropdownRef.current.contains(event.target as Node) &&
        industryDropdownRef.current &&
        !industryDropdownRef.current.contains(event.target as Node) &&
        resourceTypeDropdownRef.current &&
        !resourceTypeDropdownRef.current.contains(event.target as Node)
      ) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  return (
    <>
      {!isTablet ? (
        <>
          <Container fluid className={styles.filter_container}>
            <Heading
              headingType="h2"
              title={filterdata?.title}
              className={styles.filter_title}
            />
            <div className={styles.filter_tabs}>
              <div className={styles.dropdown_tabs}>
                {/* Services Dropdown */}
                <div
                  ref={servicesDropdownRef}
                  className={styles.dropdown_tabs_services}
                >
                  <div
                    className={styles.dropdown_service}
                    onClick={toggleDropdownServices}
                  >
                    <div className={styles.dropdown_button}>
                      {filterdata?.select_services_title}
                    </div>
                    <div className={styles.arrow}>
                      <Image
                        src={
                          openDropdown === 'services'
                            ? 'https://cdn.marutitech.com/drop_down_upside_0c7e8734bc.svg'
                            : 'https://cdn.marutitech.com/drop_down_0839569e62.svg'
                        }
                        alt={'dropdown arrow'}
                        width={24}
                        height={24}
                      />
                    </div>
                  </div>

                  {openDropdown === 'services' && (
                    <div className={styles.dropdown_content}>
                      <ul>
                        {dropDownServicesData?.map((service: any) => (
                          <li key={service.id}>
                            <label className={styles.checkbox_label}>
                              <input
                                type="checkbox"
                                checked={selectedServices.includes(
                                  service.attributes.service_title,
                                )}
                                onChange={() =>
                                  handleServiceChange(
                                    service.attributes.service_title,
                                  )
                                }
                              />
                              {service.attributes.service_title}
                            </label>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

                {/* Industries Dropdown */}
                <div
                  ref={industryDropdownRef}
                  className={styles.dropdown_tabs_services}
                >
                  <div
                    className={styles.dropdown_service}
                    onClick={toggleDropdownIndustry}
                  >
                    <div className={styles.dropdown_button}>
                      {filterdata?.select_industries_title}
                    </div>
                    <div className={styles.arrow}>
                      <Image
                        src={
                          openDropdown === 'industry'
                            ? 'https://cdn.marutitech.com/drop_down_upside_0c7e8734bc.svg'
                            : 'https://cdn.marutitech.com/drop_down_0839569e62.svg'
                        }
                        alt={'dropdown arrow'}
                        width={24}
                        height={24}
                      />
                    </div>
                  </div>

                  {openDropdown === 'industry' && (
                    <div className={styles.dropdown_content}>
                      <ul>
                        {dropDownIndustryData?.map((industry: any) => (
                          <li key={industry.id}>
                            <label className={styles.checkbox_label}>
                              <input
                                type="checkbox"
                                checked={selectedIndustries.includes(
                                  industry.attributes.industry_title,
                                )}
                                onChange={() =>
                                  handleIndustryChange(
                                    industry.attributes.industry_title,
                                  )
                                }
                              />
                              {industry.attributes.industry_title}
                            </label>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

                {/* Resource Type Dropdown */}
                <div
                  ref={resourceTypeDropdownRef}
                  className={styles.dropdown_tabs_services}
                >
                  <div
                    className={styles.dropdown_service}
                    onClick={toggleDropdownResourceType}
                  >
                    <div className={styles.dropdown_button}>
                      {filterdata?.select_resources_title}
                    </div>
                    <div className={styles.arrow}>
                      <Image
                        src={
                          openDropdown === 'resource_type'
                            ? 'https://cdn.marutitech.com/drop_down_upside_0c7e8734bc.svg'
                            : 'https://cdn.marutitech.com/drop_down_0839569e62.svg'
                        }
                        alt={'dropdown arrow'}
                        width={24}
                        height={24}
                      />
                    </div>
                  </div>

                  {openDropdown === 'resource_type' && (
                    <div className={styles.dropdown_content}>
                      <ul>
                        {dropDownResourceTypeData?.map((resourceType: any) => (
                          <li key={resourceType.id}>
                            <label className={styles.checkbox_label}>
                              <input
                                type="checkbox"
                                checked={selectedResourceType.includes(
                                  resourceType.attributes.resource_type_title,
                                )}
                                onChange={() =>
                                  handleResourceTypeChange(
                                    resourceType.attributes.resource_type_title,
                                  )
                                }
                              />
                              {resourceType.attributes.resource_type_title}
                            </label>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>

              <Button
                className={styles.clear_button}
                label={'Clear'}
                type="submit"
                onClick={() => {
                  setSelectedServices([]);
                  setSelectedIndustries([]);
                  setSelectedResourceType([]);
                }}
              />
            </div>
            <div className={styles.selected_value_container}>
              {/* Display combined selected values */}
              {combinedSelectedValues.map((value, index) => (
                <div className={styles.selected_value_box} key={index}>
                  {value}
                  <Image
                    src="https://cdn.marutitech.com/x_f7d037f941.svg"
                    alt="Remove"
                    width={24}
                    height={24}
                    className={styles.close_icon}
                    onClick={() => removeSelectedValue(value)}
                  />
                </div>
              ))}
            </div>
          </Container>
          <Container fluid className={styles.container}>
            {/* Case study cards */}
            <div className={styles.cardWrapper}>
              {filteredData.length > 0 ? (
                filteredData.slice(0, visibleCount).map((data, index) => {
                  const type = data?.type;
                  return (
                    <React.Fragment key={index}>
                      {type === 'case-study' &&
                        data?.attributes?.hero_section && (
                          <Link
                            href={`/case-study/${data?.attributes?.slug}`}
                            className={styles.link}
                          >
                            <div className={styles.card_tag}>
                              {
                                data.attributes.global_resource_type.data
                                  .attributes.resource_type_title
                              }
                            </div>
                            <div className={styles.card_preview}>
                              <ImageWithSizing
                                useThumbnail={true}
                                src={
                                  data?.attributes?.hero_section?.image?.data
                                    ?.attributes
                                }
                                alt={
                                  data?.attributes?.hero_section?.image?.data
                                    ?.attributes?.alternativeText
                                }
                                className={styles.previewImage}
                                width={384}
                                height={220}
                              />
                              <div className={styles.text_container}>
                                <div className={styles.industry_wrapper}>
                                  {data?.attributes?.hero_section?.global_industries?.data?.map(
                                    (industry, industryIndex) => (
                                      <div
                                        className={styles.industry_title}
                                        key={industryIndex}
                                      >
                                        {industry.attributes.industry_title}
                                      </div>
                                    ),
                                  )}
                                </div>
                                <div className={styles.industry_wrapper}>
                                  {data?.attributes?.hero_section?.global_services?.data?.map(
                                    (service, serviceIndex) => (
                                      <div
                                        className={styles.industry_title}
                                        key={serviceIndex}
                                      >
                                        {service.attributes.service_title}
                                      </div>
                                    ),
                                  )}
                                </div>
                              </div>
                              <Heading
                                headingType="h3"
                                title={data?.attributes?.hero_section?.title}
                                className={styles.card_title}
                              />
                            </div>
                          </Link>
                        )}

                      {type === 'event' && data?.attributes?.hero_section && (
                        <Link
                          href={`/events/${data?.attributes?.slug}`}
                          className={styles.link}
                        >
                          <div className={styles.card_tag}>
                            {
                              data.attributes.global_resource_type.data
                                .attributes.resource_type_title
                            }
                          </div>
                          <div className={styles.card_preview}>
                            <ImageWithSizing
                              useThumbnail={true}
                              src={
                                data?.attributes?.hero_section?.hero_image?.data
                                  ?.attributes
                              }
                              alt={
                                data?.attributes?.hero_section?.hero_image?.data
                                  ?.attributes?.alternativeText
                              }
                              className={styles.previewImage}
                              width={384}
                              height={220}
                            />
                            <div className={styles.text_container}>
                              <div className={styles.industry_wrapper}>
                                <div className={styles.industry_title}>
                                  Event Date:{' '}
                                  {data?.attributes?.hero_section?.date_value}
                                </div>
                              </div>
                              <div className={styles.industry_wrapper}>
                                <div className={styles.industry_title}>
                                  {data?.attributes?.hero_section?.venue_value}
                                </div>
                              </div>
                            </div>

                            <Heading
                              headingType="h3"
                              title={data?.attributes?.hero_section?.hero_title}
                              className={styles.card_title}
                            />
                          </div>
                        </Link>
                      )}

                      {type === 'ebook' && data?.attributes?.preview && (
                        <Link
                          href={`/ebooks/${data?.attributes?.slug}`}
                          className={styles.link}
                        >
                          <div className={styles.card_tag}>
                            {
                              data.attributes.global_resource_type.data
                                .attributes.resource_type_title
                            }
                          </div>
                          <div className={styles.card_preview}>
                            <ImageWithSizing
                              useThumbnail={true}
                              src={
                                data?.attributes?.preview?.preview_image?.data
                                  ?.attributes
                              }
                              alt={
                                data?.attributes?.preview?.preview_image?.data
                                  ?.attributes?.alternativeText
                              }
                              className={styles.previewImage}
                              width={384}
                              height={220}
                            />

                            <Heading
                              headingType="h3"
                              title={data?.attributes?.title}
                              className={styles.card_title}
                            />
                          </div>
                        </Link>
                      )}

                      {type === 'white-paper' && data?.attributes?.preview && (
                        <Link
                          href={`/white-paper/${data?.attributes?.slug}`}
                          className={styles.link}
                        >
                          <div className={styles.card_tag}>
                            {
                              data.attributes.global_resource_type.data
                                .attributes.resource_type_title
                            }
                          </div>
                          <div className={styles.card_preview}>
                            <ImageWithSizing
                              useThumbnail={true}
                              src={
                                data?.attributes?.preview?.preview_image?.data
                                  ?.attributes
                              }
                              alt={
                                data?.attributes?.preview?.preview_image?.data
                                  ?.attributes?.alternativeText
                              }
                              className={styles.previewImage}
                              width={384}
                              height={220}
                            />

                            <Heading
                              headingType="h3"
                              title={data?.attributes?.title}
                              className={styles.card_title}
                            />
                          </div>
                        </Link>
                      )}

                      {type === 'blog' && (
                        <Link
                          href={`/blog/${data?.attributes?.slug}`}
                          className={styles.link}
                        >
                          <div className={styles.card_tag}>Blog</div>

                          <div className={styles.card_preview}>
                            <ImageWithSizing
                              useThumbnail={true}
                              src={
                                data?.attributes?.heroSection_image?.data
                                  ?.attributes
                              }
                              alt={
                                data?.attributes?.heroSection_image?.data
                                  ?.attributes?.alternativeText
                              }
                              className={styles.previewImage}
                              width={384}
                              height={220}
                            />
                            <Button
                              className={styles.date_button}
                              label={formatPublishDate(
                                data?.attributes?.publishedAt,
                              )}
                            />

                            <Heading
                              headingType="h3"
                              title={data?.attributes?.title}
                              className={styles.card_title}
                            />
                          </div>
                        </Link>
                      )}
                    </React.Fragment>
                  );
                })
              ) : (
                <div className={styles.no_data_found}>No data found.</div>
              )}
            </div>
            {visibleCount < filteredData.length && (
              <Button
                className={styles.view_more_button}
                label={'View More'}
                onClick={handleViewMore}
              />
            )}
          </Container>
        </>
      ) : (
        <>
          <div className={styles.button_filter_mobile}>
            <Button
              className={styles.filter_button_mobile}
              label={'Filter'}
              type="submit"
              onClick={toggleFilterVisibility}
            />
          </div>

          {isFilterVisible && (
            <Container fluid className={styles.filter_container}>
              <Heading
                headingType="h2"
                title={filterdata?.title}
                className={styles.filter_title}
              />
              <div className={styles.filter_tabs}>
                <div className={styles.dropdown_tabs}>
                  {/* Services Dropdown */}
                  <div
                    ref={servicesDropdownRef}
                    className={styles.dropdown_tabs_services}
                  >
                    <div
                      className={styles.dropdown_service}
                      onClick={toggleDropdownServices}
                    >
                      <div className={styles.dropdown_button}>
                        {filterdata?.select_services_title}
                      </div>
                      <div className={styles.arrow}>
                        <Image
                          src={
                            openDropdown === 'services'
                              ? 'https://cdn.marutitech.com/drop_down_upside_0c7e8734bc.svg'
                              : 'https://cdn.marutitech.com/drop_down_0839569e62.svg'
                          }
                          alt={'dropdown arrow'}
                          width={24}
                          height={24}
                        />
                      </div>
                    </div>

                    {openDropdown === 'services' && (
                      <div className={styles.dropdown_content}>
                        <ul>
                          {dropDownServicesData?.map((service: any) => (
                            <li key={service.id}>
                              <label className={styles.checkbox_label}>
                                <input
                                  type="checkbox"
                                  checked={selectedServices.includes(
                                    service.attributes.service_title,
                                  )}
                                  onChange={() =>
                                    handleServiceChange(
                                      service.attributes.service_title,
                                    )
                                  }
                                />
                                {service.attributes.service_title}
                              </label>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  {/* Industries Dropdown */}
                  <div
                    ref={industryDropdownRef}
                    className={styles.dropdown_tabs_services}
                  >
                    <div
                      className={styles.dropdown_service}
                      onClick={toggleDropdownIndustry}
                    >
                      <div className={styles.dropdown_button}>
                        {filterdata?.select_industries_title}
                      </div>
                      <div className={styles.arrow}>
                        <Image
                          src={
                            openDropdown === 'industry'
                              ? 'https://cdn.marutitech.com/drop_down_upside_0c7e8734bc.svg'
                              : 'https://cdn.marutitech.com/drop_down_0839569e62.svg'
                          }
                          alt={'dropdown arrow'}
                          width={24}
                          height={24}
                        />
                      </div>
                    </div>

                    {openDropdown === 'industry' && (
                      <div className={styles.dropdown_content}>
                        <ul>
                          {dropDownIndustryData?.map((industry: any) => (
                            <li key={industry.id}>
                              <label className={styles.checkbox_label}>
                                <input
                                  type="checkbox"
                                  checked={selectedIndustries.includes(
                                    industry.attributes.industry_title,
                                  )}
                                  onChange={() =>
                                    handleIndustryChange(
                                      industry.attributes.industry_title,
                                    )
                                  }
                                />
                                {industry.attributes.industry_title}
                              </label>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  {/* Resource Type Dropdown */}
                  <div
                    ref={resourceTypeDropdownRef}
                    className={styles.dropdown_tabs_services}
                  >
                    <div
                      className={styles.dropdown_service}
                      onClick={toggleDropdownResourceType}
                    >
                      <div className={styles.dropdown_button}>
                        {filterdata?.select_resources_title}
                      </div>
                      <div className={styles.arrow}>
                        <Image
                          src={
                            openDropdown === 'resource_type'
                              ? 'https://cdn.marutitech.com/drop_down_upside_0c7e8734bc.svg'
                              : 'https://cdn.marutitech.com/drop_down_0839569e62.svg'
                          }
                          alt={'dropdown arrow'}
                          width={24}
                          height={24}
                        />
                      </div>
                    </div>

                    {openDropdown === 'resource_type' && (
                      <div className={styles.dropdown_content}>
                        <ul>
                          {dropDownResourceTypeData?.map(
                            (resourceType: any) => (
                              <li key={resourceType.id}>
                                <label className={styles.checkbox_label}>
                                  <input
                                    type="checkbox"
                                    checked={selectedResourceType.includes(
                                      resourceType.attributes
                                        .resource_type_title,
                                    )}
                                    onChange={() =>
                                      handleResourceTypeChange(
                                        resourceType.attributes
                                          .resource_type_title,
                                      )
                                    }
                                  />
                                  {resourceType.attributes.resource_type_title}
                                </label>
                              </li>
                            ),
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>

                {/* <Button
                  className={styles.clear_button}
                  label={'Clear'}
                  type="submit"
                  onClick={() => {
                    setSelectedServices([]);
                    setSelectedIndustries([]);
                    setSelectedResourceType([]);
                  }}
                /> */}
              </div>
              <div className={styles.selected_value_container}>
                {/* Display combined selected values */}
                {combinedSelectedValues.map((value, index) => (
                  <div className={styles.selected_value_box} key={index}>
                    {value}
                    <Image
                      src="https://cdn.marutitech.com/x_f7d037f941.svg"
                      alt="Remove"
                      width={24}
                      height={24}
                      className={styles.close_icon}
                      onClick={() => removeSelectedValue(value)}
                    />
                  </div>
                ))}
              </div>
            </Container>
          )}
          <Container fluid className={styles.container}>
            {/* Case study cards */}
            <div className={styles.cardWrapper}>
              {filteredData.length > 0 ? (
                filteredData.slice(0, visibleCount).map((data, index) => {
                  const type = data?.type;
                  return (
                    <React.Fragment key={index}>
                      {type === 'case-study' &&
                        data?.attributes?.hero_section && (
                          <Link
                            href={`/case-study/${data?.attributes?.slug}`}
                            className={styles.link}
                          >
                            <div className={styles.card_tag}>
                              {
                                data.attributes.global_resource_type.data
                                  .attributes.resource_type_title
                              }
                            </div>
                            <div className={styles.card_preview}>
                              <ImageWithSizing
                                useThumbnail={true}
                                src={
                                  data?.attributes?.hero_section?.image?.data
                                    ?.attributes
                                }
                                alt={
                                  data?.attributes?.hero_section?.image?.data
                                    ?.attributes?.alternativeText
                                }
                                className={styles.previewImage}
                                width={348}
                                height={220}
                              />
                              <div className={styles.text_container}>
                                <div className={styles.industry_wrapper}>
                                  {data?.attributes?.hero_section?.global_industries?.data?.map(
                                    (industry, industryIndex) => (
                                      <div
                                        className={styles.industry_title}
                                        key={industryIndex}
                                      >
                                        {industry.attributes.industry_title}
                                      </div>
                                    ),
                                  )}
                                </div>
                                <div className={styles.industry_wrapper}>
                                  {data?.attributes?.hero_section?.global_services?.data?.map(
                                    (service, serviceIndex) => (
                                      <div
                                        className={styles.industry_title}
                                        key={serviceIndex}
                                      >
                                        {service.attributes.service_title}
                                      </div>
                                    ),
                                  )}
                                </div>
                              </div>
                              <Heading
                                headingType="h3"
                                title={data?.attributes?.hero_section?.title}
                                className={styles.card_title}
                              />
                            </div>
                          </Link>
                        )}

                      {type === 'event' && data?.attributes?.hero_section && (
                        <Link
                          href={`/events/${data?.attributes?.slug}`}
                          className={styles.link}
                        >
                          <div className={styles.card_tag}>
                            {
                              data.attributes.global_resource_type.data
                                .attributes.resource_type_title
                            }
                          </div>
                          <div className={styles.card_preview}>
                            <ImageWithSizing
                              useThumbnail={true}
                              src={
                                data?.attributes?.hero_section?.hero_image?.data
                                  ?.attributes
                              }
                              alt={
                                data?.attributes?.hero_section?.hero_image?.data
                                  ?.attributes?.alternativeText
                              }
                              className={styles.previewImage}
                              width={348}
                              height={220}
                            />
                            <div className={styles.text_container}>
                              <div className={styles.industry_wrapper}>
                                <div className={styles.industry_title}>
                                  Event Date:{' '}
                                  {data?.attributes?.hero_section?.date_value}
                                </div>
                              </div>
                              <div className={styles.industry_wrapper}>
                                <div className={styles.industry_title}>
                                  {data?.attributes?.hero_section?.venue_value}
                                </div>
                              </div>
                            </div>
                            <Heading
                              headingType="h3"
                              title={data?.attributes?.hero_section?.hero_title}
                              className={styles.card_title}
                            />
                          </div>
                        </Link>
                      )}

                      {type === 'ebook' && data?.attributes?.preview && (
                        <Link
                          href={`/ebooks/${data?.attributes?.slug}`}
                          className={styles.link}
                        >
                          <div className={styles.card_tag}>
                            {
                              data.attributes.global_resource_type.data
                                .attributes.resource_type_title
                            }
                          </div>
                          <div className={styles.card_preview}>
                            <ImageWithSizing
                              useThumbnail={true}
                              src={
                                data?.attributes?.preview?.preview_image?.data
                                  ?.attributes
                              }
                              alt={
                                data?.attributes?.preview?.preview_image?.data
                                  ?.attributes?.alternativeText
                              }
                              className={styles.previewImage}
                              width={348}
                              height={220}
                            />

                            <Heading
                              headingType="h3"
                              title={data?.attributes?.title}
                              className={styles.card_title}
                            />
                          </div>
                        </Link>
                      )}

                      {type === 'white-paper' && data?.attributes?.preview && (
                        <Link
                          href={`/white-paper/${data?.attributes?.slug}`}
                          className={styles.link}
                        >
                          <div className={styles.card_tag}>
                            {
                              data.attributes.global_resource_type.data
                                .attributes.resource_type_title
                            }
                          </div>
                          <div className={styles.card_preview}>
                            <ImageWithSizing
                              useThumbnail={true}
                              src={
                                data?.attributes?.preview?.preview_image?.data
                                  ?.attributes
                              }
                              alt={
                                data?.attributes?.preview?.preview_image?.data
                                  ?.attributes?.alternativeText
                              }
                              className={styles.previewImage}
                              width={348}
                              height={220}
                            />

                            <Heading
                              headingType="h3"
                              title={data?.attributes?.title}
                              className={styles.card_title}
                            />
                          </div>
                        </Link>
                      )}

                      {type === 'blog' && (
                        <Link
                          href={`/blog/${data?.attributes?.slug}`}
                          className={styles.link}
                        >
                          <div className={styles.card_tag}>Blog</div>

                          <div className={styles.card_preview}>
                            <ImageWithSizing
                              src={
                                data?.attributes?.heroSection_image?.data
                                  ?.attributes
                              }
                              alt={
                                data?.attributes?.heroSection_image?.data
                                  ?.attributes?.alternativeText
                              }
                              className={styles.previewImage}
                              width={348}
                              height={220}
                            />
                            <Button
                              className={styles.date_button}
                              label={formatPublishDate(
                                data?.attributes?.publishedAt,
                              )}
                            />
                            <Heading
                              headingType="h3"
                              title={data?.attributes?.title}
                              className={styles.card_title}
                            />
                          </div>
                        </Link>
                      )}
                    </React.Fragment>
                  );
                })
              ) : (
                <div className={styles.no_data_found}>No data found.</div>
              )}
            </div>
            {visibleCount < filteredData.length && (
              <Button
                className={styles.view_more_button}
                label={'View More'}
                onClick={handleViewMore}
              />
            )}
          </Container>
        </>
      )}
    </>
  );
}
