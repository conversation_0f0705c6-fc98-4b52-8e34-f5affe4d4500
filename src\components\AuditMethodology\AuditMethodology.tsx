'use client';

import styles from './AuditMethodology.module.css';
import { Container } from 'react-bootstrap';
import Button from '@components/Button';

export default function AuditMethodology({ data }) {
  function handleCTA() {
    const contactFormElement = document.getElementById('contact-us-form');
    if (contactFormElement) {
      contactFormElement.scrollIntoView({ behavior: 'smooth' });
    }
  }

  return (
    <Container fluid className={styles.container}>
      <div className={styles.left_container}>
        {data?.title && <div className={styles.title}>{data?.title}</div>}
        {data?.description && (
          <div
            className={styles.description}
            dangerouslySetInnerHTML={{
              __html: data?.description,
            }}
          ></div>
        )}
        {data?.ctaButtonText && (
          <Button
            label={data?.ctaButtonText}
            className={styles.btn}
            onClick={handleCTA}
          />
        )}
      </div>
      <div className={styles.right_container}>
        {data?.box.map(box_data => (
          <div key={box_data?.id} className={styles.box_container}>
            {box_data?.title && (
              <div className={styles.box_data_title}>{box_data?.title}</div>
            )}
            {box_data?.description && (
              <div
                className={styles.box_data_description}
                dangerouslySetInnerHTML={{
                  __html: box_data?.description,
                }}
              ></div>
            )}
          </div>
        ))}
      </div>
    </Container>
  );
}
