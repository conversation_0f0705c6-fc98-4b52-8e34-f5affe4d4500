import React from 'react';
import Image from 'next/image';
import { Container } from 'react-bootstrap';
import style from './blogAboutauthor.module.css';

const AboutAuthor = ({ blogsAuthorData }) => {
  return (
    <Container className={style.container}>
      <div className={style.author__wrapper}>
        <div className={style.author__image__wrapper}>
          {blogsAuthorData?.image?.data[0]?.attributes?.url && (
            <Image
              className={style.author__image}
              src={
                blogsAuthorData?.image?.data[0]?.attributes?.format?.small
                  ?.url ||
                blogsAuthorData?.image?.data[0]?.attributes?.formats?.small
                  ?.url ||
                blogsAuthorData?.image?.data[0]?.attributes?.format?.medium
                  ?.url ||
                blogsAuthorData?.image?.data[0]?.attributes?.formats?.medium
                  ?.url ||
                blogsAuthorData?.image?.data[0]?.attributes?.format?.large
                  ?.url ||
                blogsAuthorData?.image?.data[0]?.attributes?.formats?.large
                  ?.url ||
                blogsAuthorData?.image?.data[0]?.attributes?.url
              }
              height="180"
              width="180"
              alt={blogsAuthorData?.name}
            />
          )}
        </div>
        <div className={style.author__details}>
          <div className={style.author__headline}>About the author</div>
          <div className={style.author__name}>{blogsAuthorData?.name}</div>
          <div
            className={style.author__desc}
            dangerouslySetInnerHTML={{ __html: blogsAuthorData?.description }}
          ></div>
        </div>
      </div>
    </Container>
  );
};
export default AboutAuthor;
