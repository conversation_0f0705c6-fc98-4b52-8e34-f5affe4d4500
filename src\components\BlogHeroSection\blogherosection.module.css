@value variables: "@styles/variables.module.css";
@value gray900, oneSpace, fontWeight500, fontWeight700, bodyTextXXSmall, halfSpace, twoSpace, threeSpace, fourSpace, fiveSpace, colorBlack, colorWhite, fifteenSpace, grayBorder, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, bodyTextXXXSSmall from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-xl-2000, breakpoint-xl-1800, breakpoint-sm-450, breakpoint-sm-550, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px from breakpoints;

.mainContainer {
  padding: 0;
  height: 100%;
  width: auto;
}

.image_content_column1 {
  position: relative;
  overflow: hidden;
}

.background_image {
  object-fit: cover;
  box-shadow: none !important;
}

.column2 {
  position: relative;
  display: flex;
  align-items: center;
  border-radius: 10px;
  filter: drop-shadow(0px 0px 0px rgba(0, 0, 0, 0));
}

.column2_div {
  position: absolute;
  left: -5rem;
  background-color: colorWhite;
  border-radius: 3px;
  padding: 30px;
  right: 5rem;
}

.column2_div_row1 {
  display: flex;
  justify-content: space-between;
}

.column2_div .category {
  font-style: normal;
  font-weight: fontWeight500;
  font-size: 14px;
  line-height: 123%;
  color: black;
  padding: 5px;
  background: brandColorOne;
  border-radius: 5px;
}

.column2_div .blog_title {
  margin-top: 20px;
  font-style: normal;
  font-weight: fontWeight700;
  font-size: 36px;
  line-height: 115%;
  color: gray900;
}

.column2_div .blog_description {
  margin-top: 20px;
  font-style: normal;
  font-weight: fontWeight500;
  font-size: 16px;
  line-height: 180%;
  color: gray900;
}

.mobile_image_div {
  position: relative;
  overflow: hidden;
  height: 465px;
}

.mobile_content_wrapper_div {
  border-radius: 10px;
  filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.25));
  padding: 25px;
  background: colorWhite;
  width: 94%;
  margin: -110px auto 0 auto;

  .table_of_contents {
    margin-top: 40px;
    font-style: normal;
    font-weight: fontWeight700;
    font-size: 18px;
    line-height: 150%;
    color: #f05443;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .table_of_contents span {
    margin-left: 10px;
  }
}

.mobile_content_wrapper_div .div_row1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mobile_content_wrapper_div .category {
  font-style: normal;
  font-weight: fontWeight700;
  font-size: 14px;
  line-height: 123%;
  color: colorBlack;
  padding: 5px;
  background: brandColorOne;
  border-radius: 5px;
}

.mobile_content_wrapper_div .time {
  font-style: normal;
  font-weight: fontWeight500;
  font-size: 14px;
  line-height: 123%;
  text-align: right;
  color: gray900;
}

.mobile_content_wrapper_div .blog_title {
  margin-top: 10px;
  font-style: normal;
  font-weight: fontWeight700;
  font-size: 30px;
  line-height: 123%;
  color: gray900;
}

.mobile_content_wrapper_div .blog_description {
  margin-top: 10px;
  font-style: normal;
  font-weight: fontWeight500;
  font-size: 16px;
  line-height: 123%;
  color: gray900;
}

.mobile_content_wrapper_div .author {
  display: flex;
  align-items: center;
  margin-top: 30px;
}
