@value variables: "@styles/variables.module.css";
@value gray400, gray300, colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-xl-1208, breakpoint-xl-1024, breakpoint-md, breakpoint-sm from breakpoints;

.container {
  display: flex;
  justify-content: center;
  padding: 80px 120px;
  flex-direction: column;
  align-items: center;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 40px 16px;
  }

  background-color: #f3f3f3;
}

.cardWrapper {
  display: flex;
  justify-content: center;
  max-width: 1192px;
  flex-wrap: wrap;
  row-gap: 40px;
  column-gap: 20px;
}

.card_title>h3 {
  color: colorBlack;

  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
  max-width: 384px;

  @media screen and (max-width: breakpoint-md) {
    max-width: 348px;
  }
}

.link {
  text-decoration: none;
}

.card_preview {
  display: flex;
  flex-direction: column;
  gap: 30px;
  position: relative;
}

.text_container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: absolute;
  left: 10px;
  top: 106px;
}

.industry_wrapper {
  display: flex;
  padding: 6px 10px;
  border-radius: 6px;
  background-color: colorBlack;
  gap: 10px;
  width: fit-content;
  max-width: 363px;
  flex-wrap: wrap;

  @media screen and (max-width: breakpoint-md) {
    max-width: 327px;
  }
}

.industry_title {
  color: colorWhite;

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding-right: 10px;
  border-right: 1px solid #cdcdcd;
}

.previewImage {
  background-color: gray400;
  border-radius: 6px;
}

.filter_container {
  display: flex;
  justify-content: left;
  gap: 40px;
  flex-direction: column;
  padding: 20px 120px;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 20px 16px;
  }

  background-color: #f3f3f3;
}

.filter_title>h2 {
  color: colorBlack;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;

  @media screen and (max-width: breakpoint-sm) {
    text-align: center;
  }
}

.filter_tabs {
  display: flex;
  justify-content: space-between;
}

.dropdown_tabs {
  display: flex;
  gap: 20px;

  @media screen and (max-width: 768px) {
    flex-direction: column;
  }
}

.dropdown_tabs_services {
  position: relative;
}

.dropdown_container {
  display: flex;
}

.dropdown_button {

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  cursor: pointer;
}

.dropdown_content {
  position: absolute;
  background-color: colorWhite;
  border-radius: 6px;
  min-width: 256px;
  z-index: 1;
  top: 67px;
  font-size: 18px;
  max-height: 310px;
  overflow-y: auto;
  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.1);
}

.dropdown_content::-webkit-scrollbar {
  width: 2px;
}

.dropdown_content::-webkit-scrollbar-thumb {
  background-color: #b8b4b4;
  border-radius: 8px;
}

.dropdown_content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.dropdown_content li {
  padding: 16px 16px 0 16px;
  cursor: pointer;
}

.dropdown_content li:hover {
  background-color: #f1f1f1;
}

.dropdown_service {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  width: 256px;
  padding: 15px 22px;
  background-color: colorWhite;
  border-radius: 6px;
}

.selected_value_container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.selected_value_box {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  background-color: #e4e4e4;
  position: relative;
  padding: 10px;
  border-radius: 6px;
}

.close_icon {
  margin-left: 12px;
  cursor: pointer;
}

.checkbox_label {
  display: flex;
  gap: 10px;
  border-bottom: 1px solid #8c8b8b;
  padding-bottom: 16px;
  cursor: pointer;
}

.clear_button {
  background-image: linear-gradient(gray300, gray300), linear-gradient(93.12deg, brandColorOne 0%, brandColorTwo 30.56%, brandColorThree 53.47%, brandColorFour 75.75%, brandColorFive 100%) !important;
  color: colorBlack !important;
  font-size: 20px !important;
  font-style: normal !important;
  font-weight: 600 !important;
  line-height: normal !important
}

.date_button {
  background-image: linear-gradient(gray300, gray300), linear-gradient(93.12deg, brandColorOne 0%, brandColorTwo 30.56%, brandColorThree 53.47%, brandColorFour 75.75%, brandColorFive 100%) !important;
  color: colorBlack !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: normal !important;
  max-width: fit-content !important;
  min-width: 0 !important;
}

.button_filter_mobile {
  display: flex;
  padding: 40px 32px;
  background-color: #f3f3f3;
}

.filter_button_mobile {
  height: 62px;
}

.paginationContainer {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.pagination {
  display: flex;
  flex-wrap: wrap;
  list-style-type: none;
  gap: 30px;
  padding: 0;
  align-items: center;
  color: colorBlack;
  flex-direction: row;
  list-style-image: none;

  @media screen and (max-width: 450px) {
    gap: 24px;
  }
}

.paginationLink {
  cursor: pointer;
  color: colorBlack;
  text-decoration: none;
}

.paginationDisabled {
  color: #ccc;
  cursor: not-allowed;
  text-decoration: none;
}

.paginationActive {
  background-color: #b41f5e;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50px;
}

.no_data_found {
  font-size: 18px;
  font-weight: 500;
}