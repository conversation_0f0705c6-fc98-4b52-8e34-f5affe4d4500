@value variables: "@styles/variables.module.css";
@value oneSpace, fontWeight700, bodyTextXXSmall, halfSpace, twoSpace, threeSpace, fourSpace, fiveSpace, colorBlack, colorWhite, fifteenSpace, grayBorder, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, bodyTextXXXSSmall from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-xl-2000, breakpoint-xl-1800, breakpoint-sm-450, breakpoint-sm-550, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px from breakpoints;

.socialMediaIcons {
  margin-top: twoSpace;
  display: flex;
}

.socialMediaIcons>div {
  margin-right: var(--twoSpace);
}

.socialMediaIcons svg {
  cursor: pointer;
}

.socialMediaIcons svg:hover {
  fill: var(--#f5f6f8);
}

.socialMediaIconsVertical {
  display: flex;
  justify-content: space-between;
  position: sticky;
}

.socialMediaIconsVertical svg:hover {
  fill: var(--#f5f6f8);
}

.socialMediaIconsVertical>div {
  margin-top: var(--threeSpace);
  cursor: pointer;
}

@media screen and (max-width: var(--breakpoint-lg)) {
  .socialMediaIconsVertical {
    display: none;
  }
}