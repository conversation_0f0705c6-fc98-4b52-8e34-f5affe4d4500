'use client';

import { Container } from 'react-bootstrap';
import Link from 'next/link';

import styles from './Breadcrumb.module.css';

export default function Breadcrumb({
  L2pageName,
  L2pageSlug,
  L3pageName,
  L3pageSlug,
  industrySlug,
  industryName,
  eventPageName,
  eventPageSlug,
}: any) {
  const pathSegments = [{ name: 'Home', href: '/' }];

  if (L2pageName && L2pageSlug) {
    pathSegments.push(
      {
        name: 'Services',
        href: '/services',
      },
      {
        name: L2pageName,
        href: `/services/${L2pageSlug}`,
      },
    );
  }
  if (L3pageName && L3pageSlug) {
    pathSegments.push({
      name: L3pageName,
      href: `/services/${L2pageSlug}/${L3pageSlug}`,
    });
  }
  if (industryName && industrySlug) {
    pathSegments.push({
      name: industryName,
      href: industrySlug,
    });
  }
  if (eventPageName && eventPageSlug) {
    pathSegments.push(
      {
        name: ' Events and webinars',
        href: '/events',
      },
      {
        name: eventPageName,
        href: `/events/${eventPageSlug}`,
      },
    );
  }
  return (
    <nav className={styles.breadcrumb}>
      {pathSegments.map((segment, index) => (
        <span key={index}>
          <Link href={segment.href}>{segment.name}</Link>
          {index < pathSegments.length - 1 && (
            <span className={styles.arrow_style}>{' > '}</span>
          )}
        </span>
      ))}
    </nav>
  );
}
