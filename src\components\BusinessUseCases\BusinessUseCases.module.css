@value variables: "@styles/variables.module.css";
@value gray300, colorGray, colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-lg, breakpoint-md, breakpoint-sm, breakpoint-sm-427 from breakpoints;

.container {
  padding: 80px 124px;
  background-color: colorBlack;
  display: flex;
  flex-direction: column;
  gap: 40px;
  user-select: none;

  @media screen and (max-width: breakpoint-md) {
    padding: 40px 32px;
  }

  @media screen and (max-width: breakpoint-sm) {
    padding: 40px 16px;
  }
}

.title_desc_section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.title h2 {
  color: colorWhite;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  /* 56px */
  letter-spacing: -0.8px;
  text-align: center;
}

.description {
  color: colorWhite;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  text-align: center;
}

.embla {
  max-width: 100%;
  margin: auto;
  --slide-height: auto;
  --slide-spacing: 20px;
  --slide-size: auto;
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
  display: flex;
}

.embla__controls {
  display: grid;
  justify-content: center;
  gap: 1.2rem;
  margin-top: 40px;
}

.card {
  max-width: 270px;
  position: relative;
  max-width: 270px;
  display: block;
  overflow: visible;
  height: 310px;
  padding: 1.5rem;
  text-decoration: none;
  color: colorWhite;
  border-radius: 6px;

  background-color: #202020;
  align-content: end;

  background-size: 100% 100%;
}

.onHoverBgImage {
  visibility: hidden;
  opacity: 0;
  transition:
    opacity 0.4s ease-in-out,
    visibility 0.4s ease-in-out;
  border: 0;
  border-radius: 6px;
  object-fit: cover;

  @media (max-width: 769px) {
    opacity: 1;
    visibility: visible;
  }
}

.card:hover .onHoverBgImage {
  opacity: 1;
  visibility: visible;
}

.contentWrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.cardOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;

  opacity: 0;
  transition: opacity 0.5s ease-out;
  z-index: 1;
  border: 0;
  border-radius: 6px;

  @media (max-width: 769px) {
    opacity: 1;
  }
}

.card:hover .cardOverlay {
  opacity: 1;
}

.cardTitle {
  z-index: 2;
}

.cardTitle>h3 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 138%;
  letter-spacing: 0.48px;
}

.cardDescription {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  z-index: 2;
  line-height: normal;
}