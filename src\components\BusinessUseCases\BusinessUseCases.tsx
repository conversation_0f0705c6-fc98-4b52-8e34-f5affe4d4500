'use client';

import React from 'react';
import { Container } from 'react-bootstrap';
import Image from 'next/image';
import Heading from '@components/Heading';
import { EmblaOptionsType } from 'embla-carousel';
import useDotButton from '@hooks/useDotButton';
import DotButton from '@components/DotButton/DotButton';
import styles from './BusinessUseCases.module.css';
import useEmblaCarousel from 'embla-carousel-react';
import classNames from '@utils/classNames';

import emblastyles from '../../styles/emlaDots.module.css';
import ImageWithSizing from '@components/ImageWithSizing';

export default function BusinessUseCases({
  dataBusinessUseCases,
  variantWhite = false,
}) {
  const OPTIONS: EmblaOptionsType = {
    align: 'start',
    dragFree: true,
  };

  const [emblaRef, emblaApi] = useEmblaCarousel(OPTIONS);
  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  return (
    <>
      <Container fluid className={styles.container}>
        <div className={styles.title_desc_section}>
          <Heading
            headingType="h2"
            title={dataBusinessUseCases?.title}
            className={styles.title}
          />
          <div
            className={styles.description}
            dangerouslySetInnerHTML={{
              __html: dataBusinessUseCases?.description,
            }}
          ></div>
        </div>
        <div className={styles.embla}>
          <div className={styles.embla__viewport} ref={emblaRef}>
            <div className={styles.embla__container}>
              {dataBusinessUseCases?.challenges_box?.map((data, index) => (
                <div className={styles.embla__slide} key={index}>
                  <div className={styles.card} key={data?.id}>
                    <div className={styles.contentWrapper}>
                      <div className={styles.cardOverlay} />
                      <ImageWithSizing
                        src={data?.image?.data?.attributes}
                        fill
                        alt="onHoverBgImage"
                        className={styles.onHoverBgImage}
                      />
                      <Heading
                        headingType="h3"
                        title={data?.title}
                        className={styles.cardTitle}
                      />
                      <div
                        className={styles.cardDescription}
                        dangerouslySetInnerHTML={{
                          __html: data?.description,
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className={styles.embla__controls}>
            <div className={emblastyles.embla__dots}>
              {scrollSnaps.length > 1 &&
                scrollSnaps.map((_, index) => (
                  <DotButton
                    key={index}
                    onClick={() => onDotButtonClick(index)}
                    className={
                      index === selectedIndex
                        ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                        : variantWhite
                          ? classNames(
                              emblastyles.embla__dot,
                              emblastyles.embla__dot_bg_white,
                            )
                          : emblastyles.embla__dot
                    }
                  />
                ))}
            </div>
          </div>
        </div>
      </Container>
    </>
  );
}
