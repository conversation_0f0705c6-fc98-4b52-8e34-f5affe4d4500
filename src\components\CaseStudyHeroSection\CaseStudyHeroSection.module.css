@value variables: "@styles/variables.module.css";
@value fontWeight400, colorBlack, colorWhite, brandColorThree, fifteenSpace, grayBorder from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-xl-2000, breakpoint-sm-450, breakpoint-sm-550, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px from breakpoints;

.main_container_listing {
  position: relative;
  overflow: hidden;
  height: 511px;
}

.background_image {
  background-color: colorBlack;
  object-fit: cover;
  transition: transform 0.5s ease;
  z-index: -1;
}

.main_container_events {
  position: relative;
  overflow: hidden;
  background: #000000a6;
  min-height: 700px;
}

.hero_listing_title > h1 {
  position: absolute;
  color: colorWhite;

  font-size: 78px;
  font-style: normal;
  font-weight: 600;
  line-height: 102%;
  bottom: 80px;
  left: 124px;

  @media screen and (min-width: breakpoint-xl-2000) {
    left: 150px;
  }

  @media screen and (max-width: breakpoint-md) {
    left: 32px;
    bottom: 40px;
  }

  @media screen and (max-width: breakpoint-sm-550) {
    left: 16px;
    font-size: 48px;
    line-height: 51px;
    letter-spacing: -0.96px;
  }
}

.main_container {
  position: relative;
  overflow: hidden;
  background: #000000d9;

  @media screen and (max-width: breakpoint-md) {
    padding: 0px 16px;
    display: flex;
    align-items: flex-end;
  }
}

.inner_container {
  display: flex;
  flex-direction: column;
}

.top_label {
  color: colorWhite;

  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 168%;
  padding: 2px 6px;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.7);
  width: fit-content;
}

.title_button_container {
  display: flex;
  flex-direction: column;
  gap: 30px;
  max-width: 1166px;
}

.hero_details_title {
  color: colorWhite;

  font-size: 52px;
  font-style: normal;
  font-weight: 600;
  line-height: 120%;
  /* 62.4px */
  letter-spacing: -1.04px;

  @media screen and (max-width: breakpoint-sm-550) {
    font-size: 28px;
    line-height: 138%;
    letter-spacing: -0.84px;
  }
}

.submitButton {
  padding: 16px 36px !important;
  width: fit-content;

  @media (max-width: breakpoint-md-767) {
    width: 100%;
  }
}

.submitButton > div {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
}

.submitButton::before {
  border-radius: 6px;
  padding: 2px;
}

.submitButtonDisabled {
  padding: 16px 36px;
  width: fit-content;
  opacity: 0.85;

  @media (max-width: breakpoint-md-767) {
    width: 100%;
  }
}

.submitButtonDisabled:hover {
  box-shadow: none !important;
  cursor: not-allowed;
}

.submitButtonDisabled > div {
  text-decoration: line-through;
  text-decoration-thickness: 2px;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
}

.submitButtonDisabled::before {
  border-radius: 6px;
  padding: 4px;
  opacity: 0.85;
}

.bottom_container {
  display: flex;
  gap: 100px;
  max-width: fit-content;

  @media screen and (max-width: breakpoint-sm-550) {
    gap: 50px;
  }
}

.multi_box {
  display: flex;
  flex-direction: row;
  gap: 16px;
  flex-wrap: wrap;
}

.expertise_delivered_container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: fit-content;
}

.expertise_delivered_title {
  color: colorWhite;

  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: 44px;

  @media screen and (max-width: breakpoint-sm-550) {
    font-size: 22px;
  }
}

.single_box {
  color: colorWhite;

  font-size: 16px;
  font-style: normal;
  font-weight: fontWeight400;
  line-height: 144%;
  padding: 8px 10px 8px 0px;
  border-right: 1px solid #cdcdcd;
}

.single_box:last-child {
  border-right: none;
}

.industry_container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: fit-content;
}

.industry_title {
  color: colorWhite;

  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: 44px;

  @media screen and (max-width: breakpoint-sm-550) {
    font-size: 22px;
  }
}

.inner_container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: fit-content;
  padding: 32.5px 32.5px 72px 124px;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 28px 32px 40px 32px;
  }

  @media screen and (max-width: breakpoint-sm-550) {
    padding: 28px 16px 40px 16px;
  }
}

.main_section {
  display: flex;
  flex-direction: column;
  gap: 100px;

  @media screen and (max-width: breakpoint-sm-550) {
    gap: 40px;
  }
}

.hero_events_page {
  font-size: 78px;
  font-style: normal;
  font-weight: 600;
  line-height: 102%;
  color: colorWhite;
  padding-top: 46px;
}

.section_conatiner {
  display: flex;
  gap: 100px;
}

.date_container {
  display: flex;
  gap: 50px;
}

.date_container_box {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.date_title {
  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: 44px;
  color: colorWhite;
}

.date_description {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 144%;
  color: colorWhite;
}

.events_inner_container {
  display: flex;
  flex-direction: column;
  gap: 30px;
  max-width: fit-content;
  padding: 20px 32.5px 80px 120px;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 28px 32px 40px 32px;
  }

  @media screen and (max-width: breakpoint-sm-550) {
    padding: 28px 16px 40px 16px;
  }
}
