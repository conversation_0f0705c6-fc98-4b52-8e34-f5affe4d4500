'use client';

import { Container } from 'react-bootstrap';
import Breadcrumb from '@components/Breadcrumb';
import Button from '@components/Button';
import Heading from '@components/Heading';
import styles from './CaseStudyHeroSection.module.css';
import ImageWithBlurPreview from '@components/ImageWithBlurPreview';

export default function CaseStudyHeroSection({
  heroData,
  variant,
  scrollToForm,
  eventPageName,
  eventPageSlug,
}: any) {
  const eventDate = heroData?.event_starting_date;
  const today = new Date().toISOString().split('T')[0]; // format :YYYY-MM-DD

  // check if event is already passed
  const isDisabled = eventDate <= today;

  return (
    <>
      {variant === 'listing_page' && (
        <Container fluid className={styles.main_container_listing}>
          <ImageWithBlurPreview
            data={heroData?.image?.data?.attributes}
            fill={true}
            quality={95}
            priority={true}
            loading="eager"
            mainClass={styles.background_image}
          />

          <Heading
            headingType="h1"
            title={heroData?.title}
            className={styles.hero_listing_title}
          />
        </Container>
      )}
      {variant === 'details_page' && (
        <Container fluid className={styles.main_container}>
          <ImageWithBlurPreview
            data={heroData?.image?.data?.attributes}
            fill={true}
            quality={95}
            priority={true}
            loading="eager"
            mainClass={styles.background_image}
          />

          <div className={styles.inner_container}>
            {heroData?.tag && (
              <div className={styles.top_label}>{heroData?.tag}</div>
            )}
            <div className={styles.main_section}>
              {heroData?.title && (
                <div className={styles.title_button_container}>
                  <h1 className={styles.hero_details_title}>
                    {heroData?.title}
                  </h1>
                  <Button
                    className={styles.submitButton}
                    label={heroData?.download_button?.title}
                    type="submit"
                    scrollToForm={scrollToForm}
                  />
                </div>
              )}
              <div className={styles.bottom_container}>
                {heroData?.global_services.data && (
                  <div className={styles.expertise_delivered_container}>
                    <h2 className={styles.expertise_delivered_title}>
                      Services We Delivered
                    </h2>
                    <div className={styles.multi_box}>
                      {heroData?.global_services?.data.map(data => (
                        <h6 className={styles.single_box} key={data?.id}>
                          {data?.attributes?.service_title}
                        </h6>
                      ))}
                    </div>
                  </div>
                )}
                {heroData?.global_industries?.data && (
                  <div className={styles.expertise_delivered_container}>
                    <h2 className={styles.expertise_delivered_title}>
                      Industry
                    </h2>
                    <div className={styles.multi_box}>
                      {heroData?.global_industries?.data.map(data => (
                        <h6 className={styles.single_box} key={data?.id}>
                          {data?.attributes?.industry_title}
                        </h6>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Container>
      )}
      {variant === 'events_page' && (
        <Container fluid className={styles.main_container_events}>
          <ImageWithBlurPreview
            data={heroData?.hero_image?.data?.attributes}
            fill={true}
            quality={95}
            priority={true}
            loading="eager"
            mainClass={styles.background_image}
          />

          <div className={styles.events_inner_container}>
            <Breadcrumb
              eventPageName={eventPageName}
              eventPageSlug={eventPageSlug}
            />
            <h1 className={styles.hero_events_page}>{heroData?.hero_title}</h1>
            <div className={styles.section_conatiner}>
              <div className={styles.date_container}>
                <div className={styles.date_container_box}>
                  <h2 className={styles.date_title}>{heroData?.date_title}</h2>
                  <div className={styles.date_description}>
                    {heroData?.date_value}
                  </div>
                </div>
                <div className={styles.date_container_box}>
                  <h2 className={styles.date_title}>{heroData?.venue_title}</h2>
                  <div className={styles.date_description}>
                    {heroData?.venue_value}
                  </div>
                </div>
              </div>

              <div className={styles.date_container_box}>
                <h2 className={styles.date_title}>
                  {heroData?.booth_number_title}
                </h2>
                <div className={styles.date_description}>
                  {heroData?.booth_number_value}
                </div>
              </div>
            </div>
            <Button
              isLink
              href={isDisabled ? '' : heroData?.button?.link}
              className={
                isDisabled ? styles.submitButtonDisabled : styles.submitButton
              }
              label={heroData?.button?.title}
              type="submit"
            />
          </div>
        </Container>
      )}
    </>
  );
}
