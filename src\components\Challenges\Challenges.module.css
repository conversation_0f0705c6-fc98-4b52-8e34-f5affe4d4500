@value variables: "@styles/variables.module.css";
@value gray300, colorBlack, colorWhite from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-lg, breakpoint-md, breakpoint-sm, breakpoint-sm-427 from breakpoints;

.container {
  padding: 80px 124px;
  background-color: gray300;
  display: flex;
  flex-direction: column;
  gap: 40px;

  @media screen and (max-width: breakpoint-md) {
    padding: 40px 32px;
  }

  @media screen and (max-width: breakpoint-sm) {
    padding: 40px 16px;
  }
}

.title_desc_section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.title h2 {
  color: colorBlack;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  /* 56px */
  letter-spacing: -0.8px;
  text-align: center;
}

.description {
  color: colorBlack;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  text-align: center;
}

.box_title h3 {
  color: colorWhite;

  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 138%;
  /* 33.12px */
  letter-spacing: 0.48px;
}

.box_description {
  color: colorWhite;

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.embla {
  max-width: 100%;
  margin: auto;
  --slide-height: auto;
  --slide-spacing: 20px;
  --slide-size: auto;
  /* position: relative; */
  /* pointer-events: none; */
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
  display: flex;
}

.columnReverse {
  flex-direction: column-reverse !important;
}

.embla__slide__number {
  max-width: 270px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: var(--slide-height);
}

.box {
  display: flex;
  flex-direction: column;
  gap: 8px;
  background-color: colorBlack;
  border-radius: 6px;
  padding: 24px;
}

.image {
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.embla__controls {
  display: grid;
  justify-content: center;
  gap: 1.2rem;
  margin-top: 40px;
}