.container {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.circle {
  position: relative;
  width: 100%;
  height: 100%;
}

.arrow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: transform 0.3s ease-in-out;
}

.container:hover .arrow {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.arrowImage {
  width: 100%;
  height: 100%;
}

.arrow_scroll {
  position: absolute;
  top: 33%;
  left: 21%;
}
