.circularTagline {
  position: relative;
  display: inline-block;
}
.arrow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.taglineImage {
  -webkit-animation: rotate 10s linear infinite forwards;
  animation: rotate 10s linear infinite forwards;
}
@-moz-keyframes rotate {
  100% {
    -moz-transform: rotate(-360deg);
  }
}
@-webkit-keyframes rotate {
  100% {
    -webkit-transform: rotate(-360deg);
  }
}
@keyframes rotate {
  100% {
    -webkit-transform: rotate(-360deg);
    transform: rotate(-360deg);
  }
}
