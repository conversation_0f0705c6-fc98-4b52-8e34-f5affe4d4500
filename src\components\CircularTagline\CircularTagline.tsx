import React from 'react';
import CircularTaglineText from '@components/Icons/CircularTaglineText';
import GradientArrow from '@components/Icons/GradientArrow';
import Link from '@components/Link';
import Image from 'next/image';

import styles from './CircularTagline.module.css';

interface CircularTaglineProps {
  tagLineHeight?: number;
  taglineWidth?: number;
  textColor?: string;
  arrowHeight?: string;
  arrowWidth?: string;
  href?: string;
  text_url?: string;
}
export default function CircularTagline({
  tagLineHeight = 70,
  taglineWidth = 70,
  textColor = 'white',
  arrowHeight = '30',
  arrowWidth = '30',
  href,
  text_url,
}: CircularTaglineProps) {
  return (
    <div className={styles.circularTagline}>
      <Link href={href}>
        <div className={styles.taglineImage}>
          <Image
            src={text_url}
            alt="circular text"
            height={tagLineHeight}
            width={taglineWidth}
          />
        </div>
        <div className={styles.arrow}>
          <GradientArrow width={arrowWidth} height={arrowHeight} />
        </div>
      </Link>
    </div>
  );
}
