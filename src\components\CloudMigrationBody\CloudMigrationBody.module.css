.hero_section {
  margin-bottom: 60px;
}

.calculator_container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section_wrapper {
  display: block;
  margin-bottom: 40px;
  padding: 40px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.hidden {
  display: none;
}

.heading {
  margin-bottom: 30px;
}

.heading h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 10px;
}

.heading p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
}

.error_message {
  background: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 12px 16px;
  border-radius: 6px;
  margin: 20px 0;
  font-weight: 500;
}

.button_wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid #eee;
}

.button_wrapper button {
  background: #007bff;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.button_wrapper button:hover {
  background: #0056b3;
  transform: scale(1.05);
}

.button_wrapper button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* Results Styles */
.results_container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.results_header {
  text-align: center;
  margin-bottom: 40px;
}

.results_heading {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 20px;
}

.cost_summary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  border-radius: 16px;
  margin-bottom: 40px;
  text-align: center;
}

.total_cost h3 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  font-weight: 600;
}

.cost_breakdown {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.cost_breakdown div {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 500;
}

.action_buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 40px;
  flex-wrap: wrap;
}

.restart_button,
.consultation_button {
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
}

.restart_button {
  background: #6c757d;
  color: white;
}

.restart_button:hover {
  background: #545b62;
}

.consultation_button {
  background: #28a745;
  color: white;
}

.consultation_button:hover {
  background: #1e7e34;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .section_wrapper {
    padding: 20px;
    margin-bottom: 20px;
  }

  .heading h2 {
    font-size: 1.5rem;
  }

  .button_wrapper {
    margin-top: 20px;
    padding-top: 20px;
  }

  .button_wrapper button {
    width: 50px;
    height: 50px;
  }

  .cost_summary {
    padding: 20px;
  }

  .total_cost h3 {
    font-size: 1.4rem;
  }

  .cost_breakdown {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .action_buttons {
    flex-direction: column;
    align-items: center;
  }

  .restart_button,
  .consultation_button {
    width: 100%;
    max-width: 300px;
  }

  .results_heading {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .calculator_container,
  .results_container {
    padding: 0 10px;
  }

  .section_wrapper {
    padding: 15px;
  }

  .heading h2 {
    font-size: 1.3rem;
  }

  .cost_summary {
    padding: 15px;
  }

  .results_heading {
    font-size: 1.8rem;
  }
}
