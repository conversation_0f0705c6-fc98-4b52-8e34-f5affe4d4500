'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import styles from './CloudMigrationBody.module.css';
import useMediaQueryState from '@hooks/useMediaQueryState';

import CostFactorInput from '@components/CostFactorInput';
import Heading from '@components/Heading';
import Button from '@components/Button';
import HeroSection from '@components/HeroSection';
import CostBreakdownChart from '@components/CostBreakdownChart';
import SavingsProjection from '@components/SavingsProjection';
import CloudMigrationForm from '@components/CloudMigrationForm';

export default function CloudMigrationBody({ body, formData }: any) {
  const router = useRouter();
  const isMobile = useMediaQueryState('(max-width: 768px)');

  // State management
  const [visibleSection, setVisibleSection] = useState(0);
  const [costData, setCostData] = useState<any[]>([]);
  const [errors, setErrors] = useState<boolean[][]>([]);
  const [costResults, setCostResults] = useState<any>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  // Initialize data structure
  useEffect(() => {
    if (body?.cost_calculator_components?.data) {
      const sections = body.cost_calculator_components.data;
      const initialData = sections.map((section: any) =>
        section.attributes.cost_factors.map(() => ['', 0, 0]) // [description, monthly_cost, one_time_cost]
      );
      const initialErrors = sections.map((section: any) =>
        section.attributes.cost_factors.map(() => true)
      );

      setCostData(initialData);
      setErrors(initialErrors);

      // Load from localStorage if available
      const savedData = localStorage.getItem('cloudMigrationCostData');
      const savedSection = localStorage.getItem('cloudMigrationVisibleSection');
      
      if (savedData) {
        setCostData(JSON.parse(savedData));
      }
      if (savedSection) {
        setVisibleSection(parseInt(savedSection));
      }
    }
  }, [body]);

  // Handle cost data updates
  const handleCostData = (
    sectionIndex: number,
    factorIndex: number,
    description: string,
    monthlyCost: number,
    oneTimeCost: number
  ) => {
    const newData = [...costData];
    newData[sectionIndex][factorIndex] = [description, monthlyCost, oneTimeCost];
    setCostData(newData);
    localStorage.setItem('cloudMigrationCostData', JSON.stringify(newData));
  };

  // Handle error states
  const handleError = (sectionIndex: number, factorIndex: number, hasError: boolean) => {
    const newErrors = [...errors];
    newErrors[sectionIndex][factorIndex] = hasError;
    setErrors(newErrors);
  };

  // Navigation handlers
  const handleVisibleSection = (section: number) => {
    setVisibleSection(section);
    localStorage.setItem('cloudMigrationVisibleSection', section.toString());
  };

  const handleNext = () => {
    if (canGoToNext()) {
      handleVisibleSection(visibleSection + 1);
    }
  };

  const handlePrevious = () => {
    if (visibleSection > 0) {
      handleVisibleSection(visibleSection - 1);
    }
  };

  // Validation
  const canGoToNext = () => {
    if (visibleSection >= costData.length) return false;
    return !errors[visibleSection]?.includes(true);
  };

  // Cost calculation logic
  const calculateCosts = () => {
    if (!canGoToNext()) return null;

    setIsCalculating(true);
    
    let totalMonthlyCost = 0;
    let totalOneTimeCost = 0;
    const sectionCosts: any[] = [];

    for (let i = 0; i < costData.length; i++) {
      const section = body?.cost_calculator_components?.data[i];
      const sectionWeight = section?.attributes?.section_weight || 1;
      
      let sectionMonthlyCost = 0;
      let sectionOneTimeCost = 0;

      for (let j = 0; j < costData[i].length; j++) {
        const [, monthlyCost, oneTimeCost] = costData[i][j];
        sectionMonthlyCost += monthlyCost * sectionWeight;
        sectionOneTimeCost += oneTimeCost * sectionWeight;
      }

      sectionCosts.push({
        name: section?.attributes?.heading,
        monthlyCost: sectionMonthlyCost,
        oneTimeCost: sectionOneTimeCost,
      });

      totalMonthlyCost += sectionMonthlyCost;
      totalOneTimeCost += sectionOneTimeCost;
    }

    const results = {
      totalMonthlyCost: Math.round(totalMonthlyCost),
      totalOneTimeCost: Math.round(totalOneTimeCost),
      annualCost: Math.round(totalMonthlyCost * 12),
      threeYearCost: Math.round(totalMonthlyCost * 36 + totalOneTimeCost),
      sectionBreakdown: sectionCosts,
      costRange: determineCostRange(totalMonthlyCost * 12 + totalOneTimeCost),
    };

    localStorage.setItem('cloudMigrationResults', JSON.stringify(results));
    setCostResults(results);
    setIsCalculating(false);
    
    return { costData, results };
  };

  // Determine cost range category
  const determineCostRange = (totalCost: number) => {
    const ranges = body?.cost_ranges || [];
    for (const range of ranges) {
      if (totalCost >= range.min_cost && (!range.max_cost || totalCost <= range.max_cost)) {
        return range;
      }
    }
    return ranges[ranges.length - 1]; // Default to highest range
  };

  // Reset calculator
  const handleRestart = () => {
    setCostData([]);
    setErrors([]);
    setCostResults(null);
    setVisibleSection(0);
    localStorage.removeItem('cloudMigrationCostData');
    localStorage.removeItem('cloudMigrationVisibleSection');
    localStorage.removeItem('cloudMigrationResults');
    window.location.reload();
  };

  return (
    <>
      {body?.hero_section && (
        <HeroSection
          heroSection={body.hero_section}
          className={styles.hero_section}
        />
      )}

      {/* Calculator Sections */}
      {!costResults && (
        <div className={styles.calculator_container}>
          {body?.cost_calculator_components?.data.map((section: any, sectionIndex: number) => (
            <div
              key={sectionIndex}
              className={
                visibleSection === sectionIndex
                  ? styles.section_wrapper
                  : styles.hidden
              }
            >
              <div className={styles.heading}>
                <h2>
                  {sectionIndex + 1}. {section?.attributes?.heading}
                </h2>
                {section?.attributes?.description && (
                  <p>{section.attributes.description}</p>
                )}
              </div>

              {section?.attributes?.cost_factors?.map((factor: any, factorIndex: number) => (
                <CostFactorInput
                  key={factorIndex}
                  factor={factor}
                  sectionIndex={sectionIndex}
                  factorIndex={factorIndex}
                  costData={costData[sectionIndex]?.[factorIndex]}
                  hasError={errors[sectionIndex]?.[factorIndex]}
                  onCostChange={handleCostData}
                  onErrorChange={handleError}
                />
              ))}

              {/* Error message */}
              {visibleSection < costData.length &&
                errors[visibleSection]?.includes(true) && (
                  <div className={styles.error_message}>
                    Please fill all the required fields.
                  </div>
                )}

              {/* Form on last section */}
              {visibleSection === costData.length - 1 && (
                <CloudMigrationForm
                  formData={formData}
                  onCalculate={calculateCosts}
                  onNext={handleNext}
                />
              )}

              {/* Navigation buttons */}
              <div className={styles.button_wrapper}>
                {visibleSection > 0 && visibleSection < costData.length && (
                  <button onClick={handlePrevious}>
                    <Image
                      src="https://cdn.marutitech.com/chevron_left_7f3e8fa9d6.svg"
                      alt="previous section"
                      width={50}
                      height={50}
                    />
                  </button>
                )}
                {visibleSection < costData.length - 1 && (
                  <button onClick={handleNext}>
                    <Image
                      src="https://cdn.marutitech.com/chevron_right_0f9e1dff3c.svg"
                      alt="next section"
                      width={50}
                      height={50}
                    />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Results Display */}
      {costResults && (
        <div className={styles.results_container}>
          <div className={styles.results_header}>
            <Heading
              title={body?.results_heading || "Your Cloud Migration Cost Estimate"}
              headingType="h2"
              className={styles.results_heading}
            />
          </div>

          <div className={styles.cost_summary}>
            <div className={styles.total_cost}>
              <h3>Total Estimated Cost</h3>
              <div className={styles.cost_breakdown}>
                <div>One-time: ${costResults.totalOneTimeCost.toLocaleString()}</div>
                <div>Monthly: ${costResults.totalMonthlyCost.toLocaleString()}</div>
                <div>Annual: ${costResults.annualCost.toLocaleString()}</div>
                <div>3-Year Total: ${costResults.threeYearCost.toLocaleString()}</div>
              </div>
            </div>
          </div>

          <CostBreakdownChart data={costResults.sectionBreakdown} />
          <SavingsProjection costResults={costResults} />

          <div className={styles.action_buttons}>
            <Button
              className={styles.restart_button}
              label={body?.restart_button?.title || "Recalculate"}
              type="button"
              onClick={handleRestart}
            />
            <Button
              className={styles.consultation_button}
              label={body?.consultation_button?.title || "Get Expert Consultation"}
              type="button"
              onClick={() => router.push(body?.consultation_button?.link || '/contact-us')}
            />
          </div>
        </div>
      )}
    </>
  );
}
