.form_container {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  margin-top: 30px;
  border: 2px solid #e9ecef;
}

.form_header {
  text-align: center;
  margin-bottom: 30px;
}

.form_header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 10px;
}

.instructions {
  font-size: 1rem;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form_row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form_group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form_group label {
  font-weight: 500;
  color: #1a1a1a;
  font-size: 0.95rem;
}

.form_group input,
.form_group textarea {
  padding: 12px 16px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  background: white;
}

.form_group input:focus,
.form_group textarea:focus {
  outline: none;
  border-color: #007bff;
}

.form_group input.error,
.form_group textarea.error {
  border-color: #dc3545;
}

.form_group textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.error_message {
  color: #dc3545;
  font-size: 0.85rem;
  font-weight: 500;
}

.consent_wrapper {
  margin: 10px 0;
}

.consent_label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #495057;
}

.consent_label.error {
  color: #dc3545;
}

.consent_label input[type="checkbox"] {
  margin: 0;
  width: 18px;
  height: 18px;
  accent-color: #007bff;
  flex-shrink: 0;
  margin-top: 2px;
}

.submit_button_row {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
}

.result_button {
  background: #007bff;
  color: white;
  border: none;
  padding: 15px 40px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 250px;
}

.result_button:hover {
  background: #0056b3;
  transform: translateY(-2px);
}

.result_button:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.container_spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.linkedInButton {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 24px;
  background: #0077b5;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.linkedInButton:hover {
  background: #005885;
  transform: translateY(-2px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .form_container {
    padding: 20px;
  }

  .form_header h3 {
    font-size: 1.3rem;
  }

  .form_row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .form_group input,
  .form_group textarea {
    padding: 10px 14px;
  }

  .result_button {
    min-width: auto;
    width: 100%;
    max-width: 300px;
  }

  .submit_button_row {
    gap: 15px;
  }

  .linkedInButton {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .form_container {
    padding: 15px;
  }

  .form_header h3 {
    font-size: 1.2rem;
  }

  .instructions {
    font-size: 0.9rem;
  }

  .form_group label {
    font-size: 0.9rem;
  }

  .form_group input,
  .form_group textarea {
    font-size: 0.95rem;
    padding: 10px 12px;
  }

  .consent_label {
    font-size: 0.85rem;
  }

  .result_button {
    font-size: 1rem;
    padding: 12px 30px;
  }

  .linkedInButton {
    font-size: 0.9rem;
    padding: 10px 20px;
  }
}

/* Focus states for accessibility */
.form_group input:focus,
.form_group textarea:focus,
.consent_label:focus-within {
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.result_button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* Form validation states */
.form_group input:valid {
  border-color: #28a745;
}

.form_group input:invalid:not(:placeholder-shown) {
  border-color: #dc3545;
}
