'use client';

import { useState } from 'react';
import Image from 'next/image';
import styles from './CloudMigrationForm.module.css';
import useForm from '@hooks/useForm';
import getUserLocation from '@utils/getUserLocation';
import Button from '@components/Button';

interface CloudMigrationFormProps {
  formData: any;
  onCalculate: () => { costData: any; results: any } | null;
  onNext: () => void;
  source?: string;
}

export default function CloudMigrationForm({
  formData,
  onCalculate,
  onNext,
  source = 'CloudMigrationCostCalculator',
}: CloudMigrationFormProps) {
  const {
    title,
    instructions,
    consent_statement,
    LinkedInButton_title,
    button,
    formFields: {
      fieldNameFor_FirstName,
      fieldNameFor_LastName,
      fieldNameFor_EmailAddress,
      fieldNameFor_CompanyName,
      fieldNameFor_PhoneNumber,
      fieldNameFor_HowCanWeHelpYou,
    },
  } = formData;

  const [isSubmitting, setIsSubmitting] = useState(false);
  const userCountryCode = getUserLocation();

  const {
    values,
    errors,
    errorMessages,
    handleChange,
    handleSubmitCloudMigration,
  } = useForm(
    {
      firstName: '',
      lastName: '',
      emailAddress: '',
      phoneNumber: '',
      howDidYouHearAboutUs: '',
      companyName: '',
      howCanWeHelpYou: '',
      consent: false,
    },
    {
      firstName: {
        empty: false,
      },
      lastName: {
        empty: false,
      },
      emailAddress: {
        empty: false,
        invalid: false,
      },
      phoneNumber: {
        empty: false,
        invalid: false,
      },
      consent: {
        empty: false,
      },
    },
    'default',
    source,
  );

  const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    setIsSubmitting(true);

    try {
      const calculationResult = onCalculate();
      
      if (calculationResult) {
        const { costData, results } = calculationResult;
        await handleSubmitCloudMigration(costData, results, onNext);
      }
    } catch (error) {
      console.error('Form submission failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={styles.form_container}>
      <div className={styles.form_header}>
        <h3>{title || 'Get Your Detailed Cost Analysis'}</h3>
        {instructions && (
          <p className={styles.instructions}>{instructions}</p>
        )}
      </div>

      <form onSubmit={onSubmit} className={styles.form}>
        <div className={styles.form_row}>
          <div className={styles.form_group}>
            <label htmlFor="firstName">
              {fieldNameFor_FirstName || 'First Name'} *
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={values.firstName}
              onChange={handleChange}
              className={errors.firstName ? styles.error : ''}
              placeholder="Enter your first name"
            />
            {errors.firstName && (
              <span className={styles.error_message}>
                {errorMessages.firstName}
              </span>
            )}
          </div>

          <div className={styles.form_group}>
            <label htmlFor="lastName">
              {fieldNameFor_LastName || 'Last Name'} *
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={values.lastName}
              onChange={handleChange}
              className={errors.lastName ? styles.error : ''}
              placeholder="Enter your last name"
            />
            {errors.lastName && (
              <span className={styles.error_message}>
                {errorMessages.lastName}
              </span>
            )}
          </div>
        </div>

        <div className={styles.form_row}>
          <div className={styles.form_group}>
            <label htmlFor="emailAddress">
              {fieldNameFor_EmailAddress || 'Email Address'} *
            </label>
            <input
              type="email"
              id="emailAddress"
              name="emailAddress"
              value={values.emailAddress}
              onChange={handleChange}
              className={errors.emailAddress ? styles.error : ''}
              placeholder="Enter your email address"
            />
            {errors.emailAddress && (
              <span className={styles.error_message}>
                {errorMessages.emailAddress}
              </span>
            )}
          </div>

          <div className={styles.form_group}>
            <label htmlFor="phoneNumber">
              {fieldNameFor_PhoneNumber || 'Phone Number'} *
            </label>
            <input
              type="tel"
              id="phoneNumber"
              name="phoneNumber"
              value={values.phoneNumber}
              onChange={handleChange}
              className={errors.phoneNumber ? styles.error : ''}
              placeholder="Enter your phone number"
            />
            {errors.phoneNumber && (
              <span className={styles.error_message}>
                {errorMessages.phoneNumber}
              </span>
            )}
          </div>
        </div>

        <div className={styles.form_group}>
          <label htmlFor="companyName">
            {fieldNameFor_CompanyName || 'Company Name'}
          </label>
          <input
            type="text"
            id="companyName"
            name="companyName"
            value={values.companyName}
            onChange={handleChange}
            placeholder="Enter your company name"
          />
        </div>

        <div className={styles.form_group}>
          <label htmlFor="howCanWeHelpYou">
            {fieldNameFor_HowCanWeHelpYou || 'How can we help you?'}
          </label>
          <textarea
            id="howCanWeHelpYou"
            name="howCanWeHelpYou"
            value={values.howCanWeHelpYou}
            onChange={handleChange}
            placeholder="Tell us about your cloud migration goals and challenges"
            rows={4}
          />
        </div>

        <div className={styles.consent_wrapper}>
          <label
            htmlFor="consent"
            className={`${styles.consent_label} ${errors.consent ? styles.error : ''}`}
          >
            <input
              type="checkbox"
              id="consent"
              name="consent"
              checked={values.consent}
              onChange={handleChange}
            />
            <span>{consent_statement || 'I agree to the terms and conditions'}</span>
          </label>
          {errors.consent && (
            <span className={styles.error_message}>
              {errorMessages.consent}
            </span>
          )}
        </div>

        <div className={styles.submit_button_row}>
          {isSubmitting ? (
            <div className={styles.container_spinner}>
              <div className={styles.spinner}></div>
            </div>
          ) : (
            <Button
              type="submit"
              className={styles.result_button}
              label="Get My Cost Analysis"
            />
          )}

          {LinkedInButton_title && (
            <a className={styles.linkedInButton} href="#">
              {LinkedInButton_title}
              <Image
                src="https://cdn.marutitech.com/linkedin_c13ca9a536.png"
                width={32}
                height={32}
                alt="LinkedIn Logo"
              />
            </a>
          )}
        </div>
      </form>
    </div>
  );
}
