'use client';

import React from 'react';
import { Container } from 'react-bootstrap';
import styles from './ClutchReviews.module.css';
import Image from 'next/image';
import Heading from '@components/Heading';
import useDotButton from '@hooks/useDotButton';
import useMediaQueryState from '@hooks/useMediaQueryState';
import classNames from '@utils/classNames';
import breakpoints from '@styles/breakpoints.module.css';
import useEmblaCarousel from 'embla-carousel-react';
import DotButton from '@components/DotButton/DotButton';
import emblastyles from '../../styles/emlaDots.module.css';
import ImageWithSizing from '@components/ImageWithSizing';

interface ClutchReviews {
  data: {
    review_image: {
      data: {
        id: number;
        attributes: {
          url: string;
          alternativeText: string;
        };
      }[];
    };
    title?: string;
    id?: number;
  };
  variantWhite?: boolean;
}

export default function ClutchReviews({
  data,
  variantWhite = true,
}: ClutchReviews) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: 'center',
    dragFree: true,
  });

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-xl-1024']})`,
  });

  return isTablet ? (
    <Container fluid className={styles.container}>
      <Heading
        title={data?.title}
        position="center"
        headingType="h4"
        className={styles.title}
      />
      <div className={styles.embla}>
        <div className={styles.embla__viewport} ref={emblaRef}>
          <div className={styles.embla__container}>
            {data?.review_image?.data?.map((item, index) => {
              return (
                <div className={styles.embla__slide} key={item?.id}>
                  <div key={item?.id} className={styles.imageWrapper}>
                    <ImageWithSizing
                      key={item?.id}
                      src={item?.attributes}
                      alt={item?.attributes?.alternativeText}
                      width={350}
                      height={439}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        <div className={styles.embla__controls}>
          <div className={emblastyles.embla__dots}>
            {scrollSnaps.length > 1 &&
              scrollSnaps.map((_, index) => (
                <DotButton
                  key={index}
                  onClick={() => onDotButtonClick(index)}
                  className={
                    index === selectedIndex
                      ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                      : variantWhite
                        ? classNames(
                            emblastyles.embla__dot,
                            emblastyles.embla__dot_bg_white,
                          )
                        : emblastyles.embla__dot
                  }
                />
              ))}
          </div>
        </div>
      </div>
    </Container>
  ) : (
    <Container fluid className={styles.container}>
      <Heading
        title={data?.title}
        position="center"
        headingType="h2"
        className={styles.title}
      />
      <div className={styles.imageContainer}>
        {data?.review_image?.data?.map((item, index) => {
          return (
            <div key={index} className={styles.imageWrapper}>
              <ImageWithSizing
                key={index}
                src={item?.attributes}
                alt={item?.attributes?.alternativeText}
                width={350}
                height={439}
              />
            </div>
          );
        })}
      </div>
    </Container>
  );
}
