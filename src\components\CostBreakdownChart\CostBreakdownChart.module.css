.chart_container {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.chart_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
}

.chart_header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.view_toggle {
  display: flex;
  background: #f1f3f4;
  border-radius: 8px;
  padding: 4px;
}

.view_toggle button {
  padding: 8px 16px;
  border: none;
  background: transparent;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
}

.view_toggle button.active {
  background: #007bff;
  color: white;
}

.view_toggle button:hover:not(.active) {
  background: #e9ecef;
}

.chart_content {
  display: flex;
  gap: 40px;
  align-items: flex-start;
  margin-bottom: 40px;
}

.donut_chart {
  flex-shrink: 0;
}

.donut_chart svg {
  max-width: 100%;
  height: auto;
}

.center_text {
  fill: #666;
}

.center_amount {
  fill: #1a1a1a;
}

.legend {
  flex: 1;
  min-width: 250px;
}

.legend_item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.legend_item:hover {
  background: #f8f9fa;
}

.legend_color {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  flex-shrink: 0;
}

.legend_content {
  flex: 1;
}

.legend_name {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.legend_value {
  font-size: 0.9rem;
  color: #666;
}

/* Bar Chart */
.bar_chart {
  border-top: 1px solid #e9ecef;
  padding-top: 30px;
}

.bar_chart h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 20px;
}

.bar_item {
  margin-bottom: 20px;
}

.bar_label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.bar_label span:first-child {
  font-weight: 500;
  color: #1a1a1a;
}

.bar_label span:last-child {
  font-weight: 600;
  color: #007bff;
}

.bar_track {
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.bar_fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.8s ease;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .chart_container {
    padding: 20px;
  }

  .chart_header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .chart_header h3 {
    font-size: 1.3rem;
    text-align: center;
  }

  .view_toggle {
    justify-content: center;
  }

  .chart_content {
    flex-direction: column;
    gap: 30px;
    align-items: center;
  }

  .donut_chart svg {
    width: 250px;
    height: 250px;
  }

  .legend {
    width: 100%;
    min-width: auto;
  }

  .bar_chart h4 {
    font-size: 1.1rem;
  }

  .bar_label {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .chart_container {
    padding: 15px;
  }

  .donut_chart svg {
    width: 200px;
    height: 200px;
  }

  .legend_item {
    padding: 8px;
  }

  .view_toggle button {
    padding: 6px 12px;
    font-size: 0.85rem;
  }

  .center_text {
    font-size: 12px;
  }

  .center_amount {
    font-size: 16px;
  }
}
