'use client';

import { useState } from 'react';
import styles from './CostBreakdownChart.module.css';

interface CostBreakdownChartProps {
  data: Array<{
    name: string;
    monthlyCost: number;
    oneTimeCost: number;
  }>;
}

export default function CostBreakdownChart({ data }: CostBreakdownChartProps) {
  const [activeView, setActiveView] = useState<'monthly' | 'onetime'>('monthly');

  const totalMonthlyCost = data.reduce((sum, item) => sum + item.monthlyCost, 0);
  const totalOneTimeCost = data.reduce((sum, item) => sum + item.oneTimeCost, 0);

  const currentTotal = activeView === 'monthly' ? totalMonthlyCost : totalOneTimeCost;

  const chartData = data.map(item => ({
    ...item,
    percentage: currentTotal > 0 ? 
      ((activeView === 'monthly' ? item.monthlyCost : item.oneTimeCost) / currentTotal) * 100 : 0,
    currentCost: activeView === 'monthly' ? item.monthlyCost : item.oneTimeCost,
  }));

  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ];

  return (
    <div className={styles.chart_container}>
      <div className={styles.chart_header}>
        <h3>Cost Breakdown by Category</h3>
        <div className={styles.view_toggle}>
          <button
            className={activeView === 'monthly' ? styles.active : ''}
            onClick={() => setActiveView('monthly')}
          >
            Monthly Costs
          </button>
          <button
            className={activeView === 'onetime' ? styles.active : ''}
            onClick={() => setActiveView('onetime')}
          >
            One-time Costs
          </button>
        </div>
      </div>

      <div className={styles.chart_content}>
        {/* Donut Chart */}
        <div className={styles.donut_chart}>
          <svg width="300" height="300" viewBox="0 0 300 300">
            <g transform="translate(150, 150)">
              {chartData.map((item, index) => {
                const startAngle = chartData
                  .slice(0, index)
                  .reduce((sum, d) => sum + (d.percentage / 100) * 360, 0);
                const endAngle = startAngle + (item.percentage / 100) * 360;
                
                const x1 = Math.cos((startAngle - 90) * Math.PI / 180) * 80;
                const y1 = Math.sin((startAngle - 90) * Math.PI / 180) * 80;
                const x2 = Math.cos((endAngle - 90) * Math.PI / 180) * 80;
                const y2 = Math.sin((endAngle - 90) * Math.PI / 180) * 80;
                
                const largeArcFlag = item.percentage > 50 ? 1 : 0;
                
                const pathData = [
                  `M ${x1} ${y1}`,
                  `A 80 80 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                  `L ${x2 * 0.5} ${y2 * 0.5}`,
                  `A 40 40 0 ${largeArcFlag} 0 ${x1 * 0.5} ${y1 * 0.5}`,
                  'Z'
                ].join(' ');

                return item.percentage > 0 ? (
                  <path
                    key={index}
                    d={pathData}
                    fill={colors[index % colors.length]}
                    stroke="white"
                    strokeWidth="2"
                  />
                ) : null;
              })}
              
              {/* Center text */}
              <text
                x="0"
                y="-5"
                textAnchor="middle"
                className={styles.center_text}
                fontSize="14"
                fontWeight="600"
              >
                Total
              </text>
              <text
                x="0"
                y="15"
                textAnchor="middle"
                className={styles.center_amount}
                fontSize="18"
                fontWeight="700"
              >
                ${currentTotal.toLocaleString()}
              </text>
            </g>
          </svg>
        </div>

        {/* Legend */}
        <div className={styles.legend}>
          {chartData.map((item, index) => (
            <div key={index} className={styles.legend_item}>
              <div
                className={styles.legend_color}
                style={{ backgroundColor: colors[index % colors.length] }}
              />
              <div className={styles.legend_content}>
                <div className={styles.legend_name}>{item.name}</div>
                <div className={styles.legend_value}>
                  ${item.currentCost.toLocaleString()} ({item.percentage.toFixed(1)}%)
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Bar Chart Alternative */}
      <div className={styles.bar_chart}>
        <h4>Detailed Breakdown</h4>
        {chartData.map((item, index) => (
          <div key={index} className={styles.bar_item}>
            <div className={styles.bar_label}>
              <span>{item.name}</span>
              <span>${item.currentCost.toLocaleString()}</span>
            </div>
            <div className={styles.bar_track}>
              <div
                className={styles.bar_fill}
                style={{
                  width: `${item.percentage}%`,
                  backgroundColor: colors[index % colors.length],
                }}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
