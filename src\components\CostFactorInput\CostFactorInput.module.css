.cost_factor {
  margin-bottom: 30px;
  padding: 25px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.factor_header {
  margin-bottom: 20px;
}

.factor_name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.factor_description {
  font-size: 0.95rem;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

.input_wrapper {
  margin-bottom: 15px;
}

/* Dropdown Styles */
.dropdown_container select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.dropdown_container select:focus {
  outline: none;
  border-color: #007bff;
}

.dropdown_container select.error {
  border-color: #dc3545;
}

/* Slider Styles */
.slider_container {
  padding: 10px 0;
}

.slider_wrapper {
  margin-bottom: 15px;
}

.slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #ddd;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #007bff;
  cursor: pointer;
  transition: background 0.3s ease;
}

.slider::-webkit-slider-thumb:hover {
  background: #0056b3;
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #007bff;
  cursor: pointer;
  border: none;
}

.slider_labels {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  font-size: 0.9rem;
  color: #666;
}

.current_value {
  font-weight: 600;
  color: #007bff;
  font-size: 1rem;
}

/* Number Input Styles */
.number_input_container {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.number_input_container input {
  flex: 1;
  min-width: 150px;
  padding: 12px 16px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.number_input_container input:focus {
  outline: none;
  border-color: #007bff;
}

.number_input_container input.error {
  border-color: #dc3545;
}

.unit {
  font-weight: 500;
  color: #666;
  white-space: nowrap;
}

.cost_display {
  width: 100%;
  margin-top: 10px;
  padding: 8px 12px;
  background: #e3f2fd;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #1976d2;
}

/* Checkbox Group Styles */
.checkbox_group {
  display: grid;
  gap: 15px;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.checkbox_group.error {
  border: 2px solid #dc3545;
  border-radius: 6px;
  padding: 15px;
}

.checkbox_item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 15px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.checkbox_item:hover {
  border-color: #007bff;
  background: #f8f9ff;
}

.checkbox_item input[type="checkbox"] {
  margin: 0;
  width: 18px;
  height: 18px;
  accent-color: #007bff;
}

.checkbox_label {
  flex: 1;
  font-size: 0.95rem;
  line-height: 1.4;
}

.option_description {
  display: block;
  color: #666;
  font-size: 0.85rem;
  margin-top: 4px;
}

.option_cost {
  margin-top: 8px;
  display: flex;
  gap: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.option_cost span {
  color: #28a745;
}

/* Error Message */
.error_message {
  color: #dc3545;
  font-size: 0.85rem;
  font-weight: 500;
  margin-top: 5px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .cost_factor {
    padding: 20px;
    margin-bottom: 20px;
  }

  .factor_name {
    font-size: 1.1rem;
  }

  .checkbox_group {
    grid-template-columns: 1fr;
  }

  .number_input_container {
    flex-direction: column;
    align-items: stretch;
  }

  .number_input_container input {
    min-width: auto;
  }

  .slider_labels {
    font-size: 0.8rem;
  }

  .current_value {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .cost_factor {
    padding: 15px;
  }

  .factor_name {
    font-size: 1rem;
  }

  .checkbox_item {
    padding: 12px;
  }

  .option_cost {
    flex-direction: column;
    gap: 5px;
  }
}
