'use client';

import { useState, useEffect } from 'react';
import styles from './CostFactorInput.module.css';

interface CostFactorInputProps {
  factor: any;
  sectionIndex: number;
  factorIndex: number;
  costData: [string, number, number] | undefined;
  hasError: boolean;
  onCostChange: (
    sectionIndex: number,
    factorIndex: number,
    description: string,
    monthlyCost: number,
    oneTimeCost: number
  ) => void;
  onErrorChange: (sectionIndex: number, factorIndex: number, hasError: boolean) => void;
}

export default function CostFactorInput({
  factor,
  sectionIndex,
  factorIndex,
  costData,
  hasError,
  onCostChange,
  onErrorChange,
}: CostFactorInputProps) {
  const [selectedValue, setSelectedValue] = useState<string>('');
  const [customValue, setCustomValue] = useState<number>(0);

  useEffect(() => {
    if (costData && costData[0]) {
      setSelectedValue(costData[0]);
    }
  }, [costData]);

  const handleOptionSelect = (option: any) => {
    const description = option.name;
    const monthlyCost = option.monthly_cost || 0;
    const oneTimeCost = option.one_time_cost || 0;

    setSelectedValue(description);
    onCostChange(sectionIndex, factorIndex, description, monthlyCost, oneTimeCost);
    onErrorChange(sectionIndex, factorIndex, false);
  };

  const handleSliderChange = (value: number) => {
    const description = `${factor.name}: ${value} ${factor.unit || ''}`;
    const monthlyCost = value * (factor.cost_multiplier || 1);
    const oneTimeCost = 0;

    setCustomValue(value);
    onCostChange(sectionIndex, factorIndex, description, monthlyCost, oneTimeCost);
    onErrorChange(sectionIndex, factorIndex, false);
  };

  const handleNumberInput = (value: number) => {
    const description = `${factor.name}: ${value} ${factor.unit || ''}`;
    const monthlyCost = value * (factor.cost_multiplier || 1);
    const oneTimeCost = 0;

    setCustomValue(value);
    onCostChange(sectionIndex, factorIndex, description, monthlyCost, oneTimeCost);
    onErrorChange(sectionIndex, factorIndex, value > 0 ? false : true);
  };

  const handleCheckboxGroup = (selectedOptions: any[]) => {
    const descriptions = selectedOptions.map(opt => opt.name).join(', ');
    const totalMonthlyCost = selectedOptions.reduce((sum, opt) => sum + (opt.monthly_cost || 0), 0);
    const totalOneTimeCost = selectedOptions.reduce((sum, opt) => sum + (opt.one_time_cost || 0), 0);

    onCostChange(sectionIndex, factorIndex, descriptions, totalMonthlyCost, totalOneTimeCost);
    onErrorChange(sectionIndex, factorIndex, selectedOptions.length === 0);
  };

  const renderInput = () => {
    switch (factor.input_type) {
      case 'dropdown':
        return (
          <div className={styles.dropdown_container}>
            <select
              value={selectedValue}
              onChange={(e) => {
                const option = factor.cost_options.find((opt: any) => opt.name === e.target.value);
                if (option) handleOptionSelect(option);
              }}
              className={hasError ? styles.error : ''}
            >
              <option value="">Select an option...</option>
              {factor.cost_options?.map((option: any, index: number) => (
                <option key={index} value={option.name}>
                  {option.name}
                  {option.monthly_cost && ` - $${option.monthly_cost}/month`}
                  {option.one_time_cost && ` - $${option.one_time_cost} one-time`}
                </option>
              ))}
            </select>
          </div>
        );

      case 'slider':
        return (
          <div className={styles.slider_container}>
            <div className={styles.slider_wrapper}>
              <input
                type="range"
                min={factor.min_value || 0}
                max={factor.max_value || 100}
                value={customValue}
                onChange={(e) => handleSliderChange(Number(e.target.value))}
                className={styles.slider}
              />
              <div className={styles.slider_labels}>
                <span>{factor.min_value || 0}</span>
                <span className={styles.current_value}>
                  {customValue} {factor.unit || ''}
                </span>
                <span>{factor.max_value || 100}</span>
              </div>
            </div>
            <div className={styles.cost_display}>
              Estimated monthly cost: ${(customValue * (factor.cost_multiplier || 1)).toLocaleString()}
            </div>
          </div>
        );

      case 'number_input':
        return (
          <div className={styles.number_input_container}>
            <input
              type="number"
              min={factor.min_value || 0}
              max={factor.max_value}
              value={customValue}
              onChange={(e) => handleNumberInput(Number(e.target.value))}
              placeholder={`Enter ${factor.unit || 'value'}`}
              className={hasError ? styles.error : ''}
            />
            <span className={styles.unit}>{factor.unit}</span>
            <div className={styles.cost_display}>
              Estimated monthly cost: ${(customValue * (factor.cost_multiplier || 1)).toLocaleString()}
            </div>
          </div>
        );

      case 'checkbox_group':
        return (
          <CheckboxGroup
            options={factor.cost_options || []}
            onChange={handleCheckboxGroup}
            hasError={hasError}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className={styles.cost_factor}>
      <div className={styles.factor_header}>
        <h4 className={styles.factor_name}>{factor.name}</h4>
        {factor.description && (
          <p className={styles.factor_description}>{factor.description}</p>
        )}
      </div>
      
      <div className={styles.input_wrapper}>
        {renderInput()}
      </div>

      {hasError && (
        <div className={styles.error_message}>
          This field is required
        </div>
      )}
    </div>
  );
}

// Checkbox Group Component
function CheckboxGroup({ options, onChange, hasError }: any) {
  const [selectedOptions, setSelectedOptions] = useState<any[]>([]);

  const handleCheckboxChange = (option: any, isChecked: boolean) => {
    let newSelected;
    if (isChecked) {
      newSelected = [...selectedOptions, option];
    } else {
      newSelected = selectedOptions.filter(opt => opt.name !== option.name);
    }
    
    setSelectedOptions(newSelected);
    onChange(newSelected);
  };

  return (
    <div className={`${styles.checkbox_group} ${hasError ? styles.error : ''}`}>
      {options.map((option: any, index: number) => (
        <label key={index} className={styles.checkbox_item}>
          <input
            type="checkbox"
            checked={selectedOptions.some(opt => opt.name === option.name)}
            onChange={(e) => handleCheckboxChange(option, e.target.checked)}
          />
          <span className={styles.checkbox_label}>
            {option.name}
            {option.description && (
              <small className={styles.option_description}>{option.description}</small>
            )}
            <div className={styles.option_cost}>
              {option.monthly_cost && <span>Monthly: ${option.monthly_cost}</span>}
              {option.one_time_cost && <span>One-time: ${option.one_time_cost}</span>}
            </div>
          </span>
        </label>
      ))}
    </div>
  );
}
