@value variables: "@styles/variables.module.css";
@value gray300, colorBlack, colorWhite from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-lg, breakpoint-md, breakpoint-sm, breakpoint-sm-427 from breakpoints;

.container {
    padding: 80px 124px;
    background-color: gray300;
    display: flex;
    flex-direction: column;
    gap: 40px;

    @media screen and (max-width: breakpoint-md) {
        padding: 40px 32px;
    }

    @media screen and (max-width: breakpoint-sm) {
        padding: 40px 16px;
    }
}

.content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.title h2 {
    color: colorBlack;

    font-size: 40px;
    font-style: normal;
    font-weight: 600;
    line-height: 140%;
    letter-spacing: -0.8px;
    text-align: center;
}

.description {
    color: colorBlack;

    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 160%;
    text-align: center;
}

.embla {
    max-width: 100%;
    margin: auto;
    --slide-height: auto;
    --slide-spacing: 20px;
    --slide-size: auto;
    /* position: relative; */
    /* pointer-events: none; */
}

.embla__viewport {
    overflow: hidden;
}

.embla__container {
    backface-visibility: hidden;
    display: flex;
    touch-action: pan-y pinch-zoom;
    margin-left: calc(var(--slide-spacing) * -1);
}

.embla__slide {
    flex: 0 0 var(--slide-size);
    min-width: 0;
    padding-left: var(--slide-spacing);
    display: flex;
}

.embla__slide__number {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-width: 283px;
    padding: 24px;
    background-color: colorWhite;
}

.box_title>h3 {
    color: colorBlack;

    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 148%;
    text-align: left;
    /* 29.6px */
}

.box_description {
    color: colorBlack;

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.embla__controls {
    display: grid;
    justify-content: center;
    gap: 1.2rem;
    margin-top: 30px;
}