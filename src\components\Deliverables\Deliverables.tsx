'use client';

import React from 'react';
import { Container } from 'react-bootstrap';
import Heading from '@components/Heading';
import useEmblaCarousel from 'embla-carousel-react';

import { EmblaOptionsType } from 'embla-carousel';
import useDotButton from '@hooks/useDotButton';
import DotButton from '@components/DotButton/DotButton';
import styles from './Deliverables.module.css';
import emblastyles from '../../styles/emlaDots.module.css';
import classNames from '@utils/classNames';

export default function Deliverables({
  datadeliverables,
  variantWhite = true,
}) {
  const OPTIONS: EmblaOptionsType = {
    align: 'start',
    dragFree: true,
  };
  const [emblaRef, emblaApi] = useEmblaCarousel(OPTIONS);
  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);
  return (
    <>
      <Container fluid className={styles.container}>
        <div className={styles.content}>
          <Heading
            className={styles.title}
            title={datadeliverables.title}
            headingType="h2"
          />
          <div
            className={styles.description}
            dangerouslySetInnerHTML={{
              __html: datadeliverables.description,
            }}
          />
        </div>
        <div className={styles.embla}>
          <div className={styles.embla__viewport} ref={emblaRef}>
            <div className={styles.embla__container}>
              {datadeliverables?.box?.map((data, index) => (
                <div className={styles.embla__slide} key={index}>
                  <div className={styles.embla__slide__number} key={index}>
                    <Heading
                      className={styles.box_title}
                      title={data.title}
                      headingType="h3"
                    />
                    <div
                      className={styles.box_description}
                      dangerouslySetInnerHTML={{
                        __html: data.description,
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className={styles.embla__controls}>
            <div className={emblastyles.embla__dots}>
              {scrollSnaps.length > 1 &&
                scrollSnaps.map((_, index) => (
                  <DotButton
                    key={index}
                    onClick={() => onDotButtonClick(index)}
                    className={
                      index === selectedIndex
                        ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                        : variantWhite
                          ? classNames(
                              emblastyles.embla__dot,
                              emblastyles.embla__dot_bg_white,
                            )
                          : emblastyles.embla__dot
                    }
                  />
                ))}
            </div>
          </div>
        </div>
      </Container>
    </>
  );
}
