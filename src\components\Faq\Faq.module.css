@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, gray from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-sm, breakpoint-xl-1400 from breakpoints;

.container {
  padding: 5rem 7.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2.5rem;
  background-color: colorBlack;
  color: colorWhite;

  @media screen and (max-width: breakpoint-md) {
    padding: 5rem 2rem;
  }
}

.title>h2 {
  text-align: center;
  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
    line-height: 138%;
  }
}

.faqsWrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 1192px;

  @media screen and (max-width: breakpoint-xl-1400) {
    width: 100%;
  }
}

.accordion__item {
  border-bottom: 2px solid transparent;
  border-image: linear-gradient(93deg,
      #febe10 0%,
      #f47a37 30.56%,
      #f05443 53.47%,
      #d91a5f 75.75%,
      #b41f5e 100%);
  border-image-slice: 1;
  width: 100%;

  @media screen and (max-width: breakpoint-sm) {
    padding: 12px 0px;
  }
}

.accordion__header>button {
  display: flex;
  justify-content: space-between;
  padding: 24px 0;
  border-bottom: 0;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 138%;
  letter-spacing: 0.48px;

  @media screen and (max-width: breakpoint-md) {
    font-size: 18px;
    letter-spacing: 0.18px;
  }
}

.accordion__body {
  color: colorWhite;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  padding-bottom: 20px;
  margin: 0 24px;

  @media screen and (max-width: breakpoint-md) {
    font-size: 16px;
    letter-spacing: 0.18px;
  }

  @media screen and (max-width: breakpoint-sm) {
    padding-bottom: 5px;
  }
}