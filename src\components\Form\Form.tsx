'use client';

import styles from './Form.module.css';
import useForm from '@hooks/useForm';
import Button from '@components/Button';

import 'react-phone-input-2/lib/style.css';
import PhoneInput from 'react-phone-input-2';
import { useState } from 'react';
import getUserLocation from '@utils/getUserLocation';

export default function Form({
  fields,
  button,
  text,
  source = 'HomePage',
}: any) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const userCountryCode = getUserLocation();

  const {
    values,
    errors,
    errorMessages,
    handleChange,
    handleBlur,
    handleSubmit,
  } = useForm(
    {
      firstName: '',
      lastName: '',
      emailAddress: '',
      phoneNumber: '',
      howDidYouHearAboutUs: '',
      companyName: '',
      howCanWeHelpYou: '',
      consent: false,
    },
    {
      firstName: {
        empty: false,
      },
      lastName: {
        empty: false,
      },
      emailAddress: {
        empty: false,
        invalid: false,
      },
      phoneNumber: {
        empty: false,
        invalid: false,
      },
      consent: {
        empty: false,
      },
    },
    'default',
    source,
  );
  const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    setIsSubmitting(true);

    try {
      await handleSubmit(event);
    } catch (error) {
      console.error('Form submission failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <>
      <form className={styles.form} onSubmit={onSubmit}>
        <div className={styles.nameFields}>
          <div className={styles.firstName}>
            <label
              className={
                errors.firstName.empty
                  ? `${styles.errorLabel} ${styles.label}`
                  : styles.label
              }
              htmlFor="firstName"
            >
              {fields.fieldNameFor_FirstName}*
            </label>
            <br />
            <input
              className={
                errors.firstName.empty
                  ? `${styles.errorInput} ${styles.input}`
                  : `${styles.input}`
              }
              type="text"
              id="firstName"
              name="firstName"
              maxLength={50}
              value={values.firstName}
              onChange={e => handleChange(e?.target)}
              onBlur={e => handleBlur(e?.target)}
            />
          </div>
          <div className={styles.lastName}>
            <label
              className={
                errors.lastName.empty
                  ? `${styles.errorLabel} ${styles.label}`
                  : styles.label
              }
              htmlFor="lastName"
            >
              {fields.fieldNameFor_LastName}*
            </label>
            <br />
            <input
              className={
                errors.lastName.empty
                  ? `${styles.errorInput} ${styles.input}`
                  : `${styles.input}`
              }
              type="text"
              id="lastName"
              name="lastName"
              maxLength={50}
              value={values.lastName}
              onChange={e => handleChange(e?.target)}
              onBlur={e => handleBlur(e?.target)}
            />
          </div>
        </div>
        <div className={styles.inputFields}>
          <label
            className={
              errors.emailAddress.empty || errors.emailAddress.invalid
                ? `${styles.errorLabel} ${styles.label}`
                : styles.label
            }
            htmlFor="emailAddress"
          >
            {fields.fieldNameFor_EmailAddress}*
          </label>
          <br />
          <input
            className={
              errors.emailAddress.empty
                ? `${styles.errorInput} ${styles.input}`
                : `${styles.input}`
            }
            type="text"
            id="emailAddress"
            name="emailAddress"
            maxLength={50}
            value={values.emailAddress}
            onChange={e => handleChange(e?.target)}
            onBlur={e => handleBlur(e?.target)}
          />
        </div>
        <div className={styles.inputFields}>
          <label
            className={
              errors.phoneNumber.empty || errors.phoneNumber.invalid
                ? `${styles.errorLabel} ${styles.label}`
                : styles.label
            }
          >
            {fields.fieldNameFor_PhoneNumber}*
          </label>
          <br />
          <div>
            <PhoneInput
              inputProps={{ id: 'phoneNumber' }}
              inputClass={
                errors.phoneNumber.empty || errors.phoneNumber.invalid
                  ? `${styles.errorPhoneInput} ${styles.phoneInput}`
                  : styles.phoneInput
              }
              buttonClass={
                errors.phoneNumber.empty || errors.phoneNumber.invalid
                  ? `${styles.errorPhoneButton} ${styles.phoneButton}`
                  : styles.phoneButton
              }
              placeholder=""
              preferredCountries={[
                'us',
                'gb',
                'sg',
                'de',
                'sa',
                'in',
                'nl',
                'au',
                'be',
                'my',
              ]}
              country={userCountryCode || 'us'}
              enableSearch={true}
              value={values.phoneNumber}
              onChange={value => handleChange({ value, name: 'phoneNumber' })}
              onBlur={e => handleBlur(e?.target)}
            />
          </div>
        </div>
        <div className={styles.inputFields}>
          <label htmlFor="howDidYouHearAboutUs" className={styles.label}>
            {fields.fieldNameFor_HowDidYouHearAboutUs}
          </label>
          <br />
          <input
            className={styles.input}
            type="text"
            id="howDidYouHearAboutUs"
            name="howDidYouHearAboutUs"
            maxLength={100}
            value={values.howDidYouHearAboutUs}
            onChange={e => handleChange(e?.target)}
            onBlur={e => handleBlur(e?.target)}
          ></input>
        </div>
        <div className={styles.inputFields}>
          <label htmlFor="companyName" className={styles.label}>
            {fields.fieldNameFor_CompanyName}
          </label>
          <br />
          <input
            className={styles.input}
            type="text"
            id="companyName"
            name="companyName"
            maxLength={50}
            value={values.companyName}
            onChange={e => handleChange(e?.target)}
            onBlur={e => handleBlur(e?.target)}
          />
        </div>
        <div className={styles.inputFields}>
          <label className={styles.label} htmlFor="howCanWeHelpYou">
            {fields.fieldNameFor_HowCanWeHelpYou}
          </label>
          <br />
          <textarea
            className={styles.textarea}
            id="howCanWeHelpYou"
            name="howCanWeHelpYou"
            maxLength={2000}
            value={values.howCanWeHelpYou}
            onChange={e => handleChange(e?.target)}
            onBlur={e => handleBlur(e?.target)}
          ></textarea>
        </div>
        <div className={styles.checkbox}>
          <input
            className={styles.input}
            type="checkbox"
            id="consent"
            name="consent"
            checked={values.consent}
            onChange={e => handleChange(e?.target)}
          />
          <label
            className={
              errors.consent.empty
                ? `${styles.errorLabel} ${styles.consentLabel}`
                : styles.consentLabel
            }
            htmlFor="consent"
          >
            {text}
          </label>
        </div>

        {isSubmitting ? (
          <div className={styles.container_spinner}>
            <div className={styles.spinner}></div>
          </div>
        ) : (
          <Button className={styles.button} label={button} type="submit" />
        )}

        <div className={styles.errorMessages}>
          <div>{errorMessages.empty && errorMessages.empty}</div>
          <div>{errorMessages.invalid && errorMessages.invalid}</div>
        </div>
      </form>
    </>
  );
}
