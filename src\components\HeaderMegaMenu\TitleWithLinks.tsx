'use client';

/* eslint-disable react/no-array-index-key */
import React, { useEffect, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import Button from '@components/Button';
import styles from '@components/Header/Header.module.css';
import RightArrowIcon from '@components/Icons/RightArrowIcon';
import Link from '@components/Link/Link';
import useMediaQueryState from '@hooks/useMediaQueryState';
import classNames from '@utils/classNames';
import breakpoints from '@styles/breakpoints.module.css';

import MenuLink from './MenuLink';
import { useRouter } from 'next/navigation';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export default function TitleWithLinks({ menuArray, button, onClick }: any) {
  const router = useRouter();
  const isLargeScreen = useMediaQueryState({
    query: `(min-width: ${breakpoints['breakpoint-xl-2442']})`,
  });
  const [isRendered, setIsRendered] = useState(false);
  useEffect(() => {
    setIsRendered(true);
  }, []);

  if (!isRendered) {
    return null;
  }

  return (
    <>
      <div
        className={classNames(
          isLargeScreen ? styles.largeDeviceSpacing : styles.megaMenuContent,
        )}
      >
        <Row>
          {menuArray.map(menu => (
            <Col
              className={classNames(
                'col-sm-12 col-md-6 col-lg-3 col-xl-3',
                styles.flexDirectionColumn,
              )}
              key={menu?.id}
            >
              <Row>
                <div className={styles.menuWrapper}>
                  <Link
                    className={styles.linkTitle}
                    href={menu?.link}
                    onClick={onClick}
                  >
                    {menu?.title}
                  </Link>
                  {menu?.sublinks?.map((sublink, index) => (
                    <MenuLink
                      linkTitle={sublink?.title}
                      href={sublink?.link}
                      key={index}
                      onClick={onClick}
                      fromSevices={true}
                    />
                  ))}
                </div>
              </Row>
            </Col>
          ))}
          <Col className="col-md-3">
            <Row />
          </Col>
          <Col className="col-md-3">
            <Row className="pt-3">
              <div
                className={classNames(
                  styles.menuWrapper,
                  styles.all_service_button,
                )}
              >
                <Button
                  type="button"
                  label={button?.title}
                  onClick={() => {
                    onClick();
                    router.push(`${button?.link}`);
                  }}
                  rightIcon={<RightArrowIcon />}
                />
              </div>
            </Row>
          </Col>
        </Row>
      </div>
      <div className={styles.bottom_border}></div>
    </>
  );
}
