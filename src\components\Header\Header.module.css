@value variables: "@styles/variables.module.css";
@value colorBlack, gray, colorWhite, fifteenSpace, grayBorder, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-sm-320, breakpoint-sm-427, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px, breakpoint-xl-2100, breakpoint-md-769, breakpoint-sm-430 from breakpoints;

.navbarWrapper {
  width: 100%;
  position: sticky;
  top: 0px;
  transition: 0.3s linear;
  background-color: colorBlack;
  z-index: 11;
}

.hidden {
  z-index: 11;
  position: sticky;
  top: -112px;
  transition: 0.3s linear;
}

.search-button {
  font-size: 20px;
}

.navLink,
.navLink_mobile {
  color: colorWhite;
  margin: 0 10px;
  font-size: 18px;

  @media screen and (min-width: 993px) {
    margin: 0 5px;
  }

  @media screen and (max-width: breakpoint-lg) {
    padding: 10px 0;
    margin: 0;
  }

  @media (max-width: breakpoint-lg) {
    font-size: 22px;
    font-weight: 400;
    border-bottom: 1px solid grayBorder;
  }

  @media (max-width: breakpoint-sm-427) {
    font-size: 20px;
    font-weight: 400;
    border-bottom: 1px solid grayBorder;
  }

  @media screen and (min-width: 993px) and (max-width: 1107px) {
    font-size: 14px;
  }

  @media screen and (min-width: 1600px) {
    font-size: 20px;
  }
}

.navItem {
  color: colorWhite;
  margin: 0;
  font-size: 16px;

  @media screen and (min-width: 1185px) {
    margin: 0 10px;
  }

  @media (max-width: breakpoint-lg) {
    font-size: 22px;
    font-weight: 400;
    border-bottom: 1px solid grayBorder;
    margin: 0;
  }

  @media (max-width: breakpoint-sm-427) {
    font-size: 20px;
    font-weight: 400;
    border-bottom: 1px solid grayBorder;
  }
}

.navItem:hover .navLink {
  color: brandColorThree !important;
}

.link:hover,
.linkTitle:hover,
.linkTitle_others:hover {
  color: brandColorThree !important;
}

.navbarCollapse {
  flex-grow: 0;
  width: 100%;

  @media (max-width: breakpoint-lg) {
    margin-top: 16px;
  }
}

.nav_dis {
  display: block !important;
}

.nav_nav_style {
  display: none;
}

.applicationContainer {
  max-width: 2236px;
  background-color: colorBlack;

  @media (min-width: breakpoint-sm-320) {
    padding-left: 20px;
    padding-right: 20px;
  }

  @media (min-width: breakpoint-md) {
    padding-left: 32px;
    padding-right: 32px;
  }

  @media (min-width: breakpoint-xl-1440) {
    padding: 0 32px;
  }

  @media screen and (min-width: 2300px) {
    padding: 0;
  }
}

.nav {
  margin-left: auto;

  @media (max-width: breakpoint-lg) {
    height: 100vh;
  }
}

.arrowIcon {
  padding-left: 5px;
}

.search,
.search::before,
.search:hover {
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
  min-width: auto !important;
  min-height: auto !important;
  border: 0;
}

.searchWrapper {
  align-self: center;

  @media (min-width: breakpoint-lg) {
    margin-left: 25px;
  }

  @media (max-width: breakpoint-lg-991px) {
    margin-left: 20px;
  }
}

@media (max-width: breakpoint-md) {
  .navbar-nav .nav-link {
    text-align: center;
  }
}

.navBarToggler {
  position: relative;
  padding: 0;
  border: 0;
  border-radius: 0;
  transition: 0.8s ease-in-out;
  margin-right: 40px;
  min-height: 40px;

  @media (max-width: breakpoint-lg-991px) {
    padding-left: 10px;
  }
}

.menuIcons {
  position: absolute;
  top: 0;
  width: 40px;
  height: 40px;
  transition:
    transform 0.3s,
    opacity 0.3s;
  transition-timing-function: ease-in-out;
  opacity: 1;
  transform: rotate(0deg);
}

.clicked {
  transform: rotate(90deg);
  opacity: 0;
}

.hide {
  @media (max-width: breakpoint-lg) {
    display: none;
  }
}

.brandContainer {
  display: flex;
  gap: 5px;
  align-items: center;
}

.visibility {
  @media (min-width: breakpoint-sm-320) {
    margin-top: 24px;
    display: block;
    width: 100%;
  }

  @media (min-width: breakpoint-md) {
    display: none;
    /* margin: 0; */
  }

  @media (min-width: breakpoint-lg-991px) {
    display: block;
    width: auto;
    margin: 0;
  }
}

.link {
  color: #cdcdcd;
  font-weight: 400;
  display: block;
  padding-top: 16px;
  text-decoration: none;
  transition: 0.1s ease-in-out;

  @media (max-width: 1024px) {
    font-size: 16px;
  }

  @media (max-width: breakpoint-sm-427) {
    padding-top: 10px;
  }
}

.menuWrapper {
  padding-top: 24px;
  padding-bottom: 24px;

  @media (max-width: breakpoint-xl-1024) {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  @media (max-width: breakpoint-sm-427) {
    padding-top: 10px;
    padding-bottom: 10px;
  }
}

.linkTitle {
  font-weight: 700;
  color: colorWhite;
  text-decoration: none;
  transition: 0.1s ease-in-out;

  @media (max-width: breakpoint-xl-1024) {
    font-size: 16px;
  }
}

.megaMenuContent {
  padding: 40px;
  opacity: 1;

  @media screen and (max-width: 2235px) {
    width: 100%;
    padding: 40px;
  }

  @media screen and (min-width: 2236px) {
    width: 2236px;
    padding: 40px 0;
  }

  @media (max-width: breakpoint-md) {
    padding: 10px 20px;
    border: none;
  }

  @media (max-width: breakpoint-sm-427) {
    padding: 10px 20px;
    border: none;
  }
}

.largeDeviceSpacing {
  padding: 40px;

  @media screen and (max-width: 2235px) {
    width: 100%;
    padding: 40px;
  }

  @media screen and (min-width: 2236px) {
    width: 2236px;
    padding: 40px 0;
  }
}

.megamenu {
  background-color: gray;
  color: white;
  overflow: hidden;
  visibility: hidden;
  max-height: 0px;
  opacity: 0;
  position: absolute;
  left: 0;
  right: 0;
  justify-items: center;

  @media (max-width: breakpoint-lg) {
    overflow: hidden;
    position: relative;
  }

  @media (max-width: breakpoint-sm-430) {
    overflow: hidden;
    position: static;
  }
}

.navItem:hover .megamenu {
  max-height: 1000px;
  opacity: 1;
  visibility: visible;
  border-top: 24px solid colorBlack;

  @media (max-width: breakpoint-lg) {
    max-height: 300px;
    overflow: auto;
  }

  @media (max-width: breakpoint-sm-427) {
    max-height: 200px;
    overflow: auto;
  }
}

.navItem:hover .megaMenuContent {
  opacity: 1;
}

.latestBlogWrapper {
  width: 283px;

  @media (max-width: breakpoint-sm-427) {
    width: 240px;
  }
}

.blogHeading {
  font-weight: 600 !important;
  background: linear-gradient(90deg, brandColorOne, brandColorFour);
  -webkit-background-clip: text;
  color: transparent;
  display: inline-block;
}

.blogTitle {
  padding-top: 10px;
}

.blogCTALink {
  padding-top: 16px;
  text-align: end;
}

.ctaLink {
  text-decoration: none;
  font-size: 14px;
  color: colorWhite;
}

.brandsButton {
  margin-top: 44px;
}

.flexDirectionColumn {
  @media (max-width: breakpoint-sm-427) {
    flex: none;
  }
}

.accordion-body {
  max-height: 160px;
  overflow: auto;
  padding: 0;
}

.all_service_button {
  width: max-content;
}

.megamenu::-webkit-scrollbar {
  width: 2px;
}

.megamenu::-webkit-scrollbar-thumb {
  background-color: #b8b4b4;
  border-radius: 8px;
}

.mtech_logo {
  @media screen and (min-width: 2200px) {
    width: 277px;
    height: 47px;
  }
}

.linkTitle_others {
  color: colorWhite;
  font-weight: 700;
  display: block;
  padding-top: 16px;
  text-decoration: none;
  transition: 0.1s ease-in-out;

  @media (max-width: 1024px) {
    font-size: 16px;
  }

  @media (max-width: breakpoint-sm-427) {
    padding-top: 10px;
  }
}

.bottom_border {
  height: 2px;
  width: 100%;
  background: linear-gradient(
    93deg,
    #febe10 0%,
    #f47a37 30.56%,
    #f05443 53.47%,
    #d91a5f 75.75%,
    #b41f5e 100%
  );
}

.hidden_override {
  top: 0;
}
