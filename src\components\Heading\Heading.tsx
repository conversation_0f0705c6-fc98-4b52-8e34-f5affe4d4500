/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable react/no-danger-with-children */
import React from 'react';
import classNames from '@utils/classNames';
import typography from '@styles/typography.module.css';

import styles from './Heading.module.css';
interface IButtonProps {
  headingType: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  title?: string;
  position?: 'center' | 'left' | 'right';
  style?: object;
  className?: string;
  richTextValue?: string;
}

export default function Heading({
  headingType,
  title,
  position,
  style,
  className,
  richTextValue,
}: IButtonProps) {
  const renderHeading = (heading?: string, label?: string) => {
    switch (heading) {
      case 'h1':
        return richTextValue ? (
          <h1
            className={typography.h1}
            style={style}
            dangerouslySetInnerHTML={{ __html: richTextValue }}
          />
        ) : (
          <h1 className={typography.h1} style={style}>
            {label}
          </h1>
        );
      case 'h2':
        return richTextValue ? (
          <h2
            className={typography.h2}
            style={style}
            dangerouslySetInnerHTML={{ __html: richTextValue }}
          />
        ) : (
          <h2 className={typography.h2} style={style}>
            {label}
          </h2>
        );
      case 'h3':
        return richTextValue ? (
          <h3
            className={typography.h3}
            style={style}
            dangerouslySetInnerHTML={{ __html: richTextValue }}
          />
        ) : (
          <h3 className={typography.h3} style={style}>
            {label}
          </h3>
        );
      case 'h4':
        return richTextValue ? (
          <h4
            className={typography.h4}
            style={style}
            dangerouslySetInnerHTML={{ __html: richTextValue }}
          />
        ) : (
          <h4 className={typography.h4} style={style}>
            {label}
          </h4>
        );
      case 'h5':
        return richTextValue ? (
          <h5
            className={typography.h5}
            style={style}
            dangerouslySetInnerHTML={{ __html: richTextValue }}
          />
        ) : (
          <h5 className={typography.h5} style={style}>
            {label}
          </h5>
        );
      case 'h6':
        return richTextValue ? (
          <h6
            className={typography.h6}
            style={style}
            dangerouslySetInnerHTML={{ __html: richTextValue }}
          />
        ) : (
          <h6 className={typography.h6} style={style}>
            {label}
          </h6>
        );
      default:
        return null;
    }
  };
  return (
    <div className={classNames(styles[position], className)}>
      {renderHeading(headingType, title)}
    </div>
  );
}
