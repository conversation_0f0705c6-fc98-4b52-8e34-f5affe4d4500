'use client';

import React, { useEffect, useState } from 'react';
import { Container } from 'react-bootstrap';
import Image from 'next/image';
import Autoplay from 'embla-carousel-autoplay';
import Fade from 'embla-carousel-fade';
import useEmblaCarousel from 'embla-carousel-react';
import CircularButtonWithArrow from '@components/CircularButtonWithArrow';
import useMediaQueryState from '@hooks/useMediaQueryState';
import breakpoints from '@styles/breakpoints.module.css';

import ImageWithBlurPreview from '@components/ImageWithBlurPreview';

import { DotButton, useDotButton } from './CarouselDotButton';
import styles from './HeroSectionHome.module.css';
import Link from 'next/link';
import Heading from '@components/Heading';
import classNames from '@utils/classNames';
import ImageWithSizing from '@components/ImageWithSizing';

export default function HomeHeroSection(props) {
  const { slides, varaint, resourcesSlide } = props;
  const slideData = slides?.data?.attributes?.hero_section;

  const [isEmblaReady, setIsEmblaReady] = useState(false);

  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true }, [
    Fade(),
    Autoplay({
      delay: 6000,
      stopOnInteraction: true,
    }),
  ]);

  useEffect(() => {
    if (emblaApi) {
      setIsEmblaReady(true);
    }
  }, [emblaApi]);

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  const isMobile = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-sm']})`,
  });

  const blurPlaceholder =
    varaint === 'primary'
      ? slideData[0]?.image?.data?.attributes?.formats?.thumbnail?.url
      : '';
  return (
    <>
      {/* Render Blurred Image Outside Embla Initially */}
      {varaint === 'primary' && !isEmblaReady && (
        <div className={styles.embla_placeholder}>
          <Image
            src={blurPlaceholder}
            alt="Blur Placeholder"
            fill
            priority
            loading="eager"
            className={classNames(styles.embla__slide_image, 'blur')}
          />
          <div className={styles.inner_container_blur}>
            <div className={styles.section_items}>
              <div className={`${styles.section_top}`}>
                <Heading
                  title={slideData[0]?.title_description?.title}
                  headingType="h1"
                  className={styles.carousel_title}
                />
                <div
                  className={styles.homepage__desc}
                  dangerouslySetInnerHTML={{
                    __html: slideData[0]?.title_description?.description,
                  }}
                />
                <Link href={`${slideData[0]?.link_url}`}>
                  <CircularButtonWithArrow variant="medium" />
                </Link>
              </div>
              <div className={styles.bottom_section_controls}></div>
            </div>
          </div>
        </div>
      )}

      {/* Render Embla Carousel */}
      {varaint === 'primary' && (
        <section
          className={styles.embla}
          style={{ display: isEmblaReady ? 'block' : 'none' }}
        >
          <div className={styles.embla__viewport} ref={emblaRef}>
            <div className={styles.embla__container}>
              {emblaApi &&
                slideData?.map((data, index) => (
                  <React.Fragment key={index}>
                    {isMobile ? (
                      <div className={styles.embla__slide_mobile} key={index}>
                        <ImageWithSizing
                          src={data?.image?.data?.attributes}
                          width={576}
                          height={245}
                          loading="eager"
                          className={styles.thumbnail_image}
                        />
                        <div className={styles.main_section_mobile}>
                          <div className={styles.section_items_mobile}>
                            <div className={styles.section_top_mobile}>
                              <Heading
                                title={data?.title_description?.title}
                                headingType="h1"
                                className={styles.carousel_title_mobile}
                              />
                              <div
                                className={styles.homepage__desc_mobile}
                                dangerouslySetInnerHTML={{
                                  __html: data?.title_description?.description,
                                }}
                              />
                            </div>
                            {data?.open_link_in_new_tab === true ? (
                              <Link
                                href={`${data?.link_url}`}
                                className={styles.circular_button_mobile}
                                target="_blank"
                              >
                                <CircularButtonWithArrow variant="medium" />
                              </Link>
                            ) : (
                              <Link
                                href={`${data?.link_url}`}
                                className={styles.circular_button_mobile}
                              >
                                <CircularButtonWithArrow variant="medium" />
                              </Link>
                            )}
                          </div>
                          <div
                            className={styles.bottom_section_controls_mobile}
                          >
                            <div className={styles.service_title}>
                              {data?.banner_name}
                              {data?.service_name && ` / ${data?.service_name}`}
                            </div>
                            <div className={styles.embla__controls}>
                              <div className={styles.embla__dots}>
                                {scrollSnaps.map((_, index) => (
                                  <DotButton
                                    key={index}
                                    onClick={() => onDotButtonClick(index)}
                                    className={
                                      index === selectedIndex
                                        ? `${styles.embla__dot_mobile} ${styles.embla__dot_selected_mobile}`
                                        : styles.embla__dot_mobile
                                    }
                                  />
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <Container
                        fluid
                        className={styles.embla__slide}
                        key={index}
                      >
                        <ImageWithSizing
                          src={data?.image?.data?.attributes}
                          fill={true}
                          loading="eager"
                          className={styles.embla__slide_image}
                        />
                        <div className={styles.inner_container}>
                          <div className={styles.section_items}>
                            <div
                              className={`${styles.section_top} ${index === selectedIndex ? styles.section_top_active : ''}`}
                            >
                              <Heading
                                title={data?.title_description?.title}
                                headingType="h1"
                                className={styles.carousel_title}
                              />
                              <div
                                className={styles.homepage__desc}
                                dangerouslySetInnerHTML={{
                                  __html: data?.title_description?.description,
                                }}
                              />
                              {data?.open_link_in_new_tab === true ? (
                                <Link
                                  href={`${data?.link_url}`}
                                  className={styles.circular_button}
                                  target="_blank"
                                >
                                  <CircularButtonWithArrow variant="medium" />
                                </Link>
                              ) : (
                                <Link
                                  href={`${data?.link_url}`}
                                  className={styles.circular_button}
                                >
                                  <CircularButtonWithArrow variant="medium" />
                                </Link>
                              )}
                            </div>
                          </div>
                          <div className={styles.bottom_section_controls}>
                            <div className={styles.service_title}>
                              {data?.banner_name}
                              {data?.service_name && ` / ${data?.service_name}`}
                            </div>
                            <div className={styles.embla__controls}>
                              <div className={styles.embla__dots}>
                                {scrollSnaps.map((_, index) => (
                                  <DotButton
                                    key={index}
                                    onClick={() => onDotButtonClick(index)}
                                    className={
                                      index === selectedIndex
                                        ? `${styles.embla__dot} ${styles.embla__dot_selected}`
                                        : styles.embla__dot
                                    }
                                  />
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      </Container>
                    )}
                  </React.Fragment>
                ))}
            </div>
          </div>
        </section>
      )}
      {varaint === 'resources' && (
        <section className={styles.embla_resources}>
          <div className={styles.embla__viewport_resources} ref={emblaRef}>
            <div className={styles.embla__container}>
              {emblaApi &&
                resourcesSlide?.map((data, index: number) => (
                  <React.Fragment key={index}>
                    {isMobile ? (
                      <div className={styles.embla__slide_mobile} key={index}>
                        {data?.image?.data?.attributes?.url && (
                          <Image
                            src={data?.image?.data?.attributes?.url}
                            alt="Image not found"
                            width={576}
                            height={245}
                          />
                        )}
                        <div className={styles.main_section_mobile_resources}>
                          <Link
                            href={data?.link}
                            className={styles.carousel_resources_link}
                          >
                            <div className={styles.section_top_mobile}>
                              <Heading
                                title={data?.title}
                                headingType="h1"
                                className={
                                  styles.carousel_title_resources_mobile
                                }
                              />
                              <div
                                className={
                                  styles.homepage__desc_mobile_resources
                                }
                                dangerouslySetInnerHTML={{
                                  __html: data?.description,
                                }}
                              />
                            </div>
                          </Link>
                          <div
                            className={styles.bottom_section_controls_mobile}
                          >
                            <div
                              className={styles.service_title_resources_mobile}
                            >
                              {data?.banner_name}
                              {data?.service_name && ` / ${data?.service_name}`}
                            </div>
                            <div className={styles.embla__controls}>
                              <div className={styles.embla__dots}>
                                {scrollSnaps.map((_, index: number) => (
                                  <DotButton
                                    key={index}
                                    onClick={() => onDotButtonClick(index)}
                                    className={
                                      index === selectedIndex
                                        ? `${styles.embla__dot_mobile} ${styles.embla__dot_selected_mobile}`
                                        : styles.embla__dot_mobile
                                    }
                                  />
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <Container
                        fluid
                        className={styles.embla__slide_resources}
                        key={index}
                      >
                        <div className={styles.inner_container_resources}>
                          <div className={styles.section_items}>
                            <div
                              className={`${styles.section_top_resources} ${index === selectedIndex ? `${styles?.section_top_active}` : ''}`}
                            >
                              <Link
                                href={data?.link}
                                className={styles.carousel_resources_link}
                              >
                                <Heading
                                  title={data?.title}
                                  headingType="h1"
                                  className={styles.carousel_title_resources}
                                />
                                <div
                                  className={styles.homepage__desc_resources}
                                  dangerouslySetInnerHTML={{
                                    __html: data?.description,
                                  }}
                                />
                              </Link>
                            </div>
                          </div>
                          <div className={styles.bottom_section_controls}>
                            <div className={styles.service_title_resources}>
                              {data?.banner_name}
                              {data?.service_name && ` / ${data?.service_name}`}
                            </div>
                            <div className={styles.embla__controls}>
                              <div className={styles.embla__dots}>
                                {scrollSnaps.map((_, index: number) => (
                                  <DotButton
                                    key={index}
                                    onClick={() => onDotButtonClick(index)}
                                    className={
                                      index === selectedIndex
                                        ? `${styles.embla__dot} ${styles.embla__dot_selected}`
                                        : styles.embla__dot
                                    }
                                  />
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      </Container>
                    )}
                  </React.Fragment>
                ))}
            </div>
          </div>
        </section>
      )}
    </>
  );
}
