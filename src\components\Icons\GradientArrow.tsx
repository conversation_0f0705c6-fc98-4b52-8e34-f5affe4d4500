import React from 'react';

export default function GradientArrow({
  width = '30',
  height = '30',
}: {
  width?: string;
  height?: string;
}) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.06119 20.1101L23.8747 9.82547"
        stroke="url(#paint0_linear_3588_3007)"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.82562 6.06104L23.8747 9.82547L20.1103 23.8745"
        stroke="url(#paint1_linear_3588_3007)"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_3588_3007"
          x1="21.6662"
          y1="12.2139"
          x2="8.64079"
          y2="17.5074"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#B41F5E" />
          <stop offset="0.311949" stopColor="#D91A5F" />
          <stop offset="0.58847" stopColor="#F05443" />
          <stop offset="0.795057" stopColor="#F47A37" />
          <stop offset="1" stopColor="#FEBE10" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_3588_3007"
          x1="21.6662"
          y1="12.2139"
          x2="8.64079"
          y2="17.5074"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#B41F5E" />
          <stop offset="0.311949" stopColor="#D91A5F" />
          <stop offset="0.58847" stopColor="#F05443" />
          <stop offset="0.795057" stopColor="#F47A37" />
          <stop offset="1" stopColor="#FEBE10" />
        </linearGradient>
      </defs>
    </svg>
  );
}
