'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import classNames from '@utils/classNames';
import styles from './ImageWithBlurPreview.module.css';

export default function ImageWithBlurPreview({
  data,
  width,
  height,
  fill,
  quality,
  priority,
  unoptimized,
  loading,
  mainClass,
  prefferedSize,
}: {
  data: any;
  width?: any;
  height?: any;
  fill?: any;
  unoptimized?: any;
  quality?: any;
  priority?: any;
  loading?: any;
  mainClass: any;
  prefferedSize?: 'large' | 'medium' | 'small';
}) {
  const [isImageLoaded, setIsImageLoaded] = useState(false);

  return (
    <>
      {!isImageLoaded && (
        <Image
          src={data?.formats?.thumbnail?.url}
          alt={data?.alternativeText || 'Hero image'}
          width={width}
          height={height}
          fill={fill}
          className={classNames(mainClass, 'blur')}
          priority={priority}
          loading={loading}
        />
      )}
      <Image
        src={
          prefferedSize === 'large'
            ? data?.format?.large?.url || data?.formats?.large?.url || data?.url
            : prefferedSize === 'medium'
              ? data?.format?.medium?.url ||
                data?.formats?.medium?.url ||
                data?.format?.large?.url ||
                data?.formats?.large?.url ||
                data?.url
              : prefferedSize === 'small'
                ? data?.format?.small?.url ||
                  data?.formats?.small?.url ||
                  data?.format?.medium?.url ||
                  data?.formats?.medium?.url ||
                  data?.format?.large?.url ||
                  data?.formats?.large?.url ||
                  data?.url
                : data?.url
        }
        width={width}
        height={height}
        fill={fill}
        alt={data?.alternativeText || 'Hero Image'}
        className={classNames(
          mainClass,
          isImageLoaded ? styles.image_visible : styles.image_hidden,
        )}
        quality={quality}
        priority={priority}
        loading={loading}
        unoptimized={unoptimized}
        onLoad={() => setIsImageLoaded(true)}
      />
    </>
  );
}
