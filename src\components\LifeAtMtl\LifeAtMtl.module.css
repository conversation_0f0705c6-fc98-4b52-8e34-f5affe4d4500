@value variables: "@styles/variables.module.css";
@value colorWhite, colorBlack from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-md, breakpoint-xl-1440 from breakpoints;

.sectionWrapper {
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.content {
  padding: 5rem 9.375rem 0 9.375rem;
  display: flex;
  flex-direction: column;
  gap: 30px;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 7.75rem 0 7.75rem;
  }

  @media (max-width: breakpoint-md) {
    padding: 5rem 2rem 0 2rem;
  }

  @media (max-width: breakpoint-sm) {
    padding: 5rem 1rem 0 1rem;
  }
}

.title h3 {
  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
}

.description {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
}

.embla {
  max-width: 100%;
  margin: auto;
  --slide-height: 17rem;
  --slide-spacing: 0;
  --slide-size: auto;
  position: relative;
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
  display: flex;
}

.embla__slide__number {
  display: flex;
  height: var(--slide-height);
}