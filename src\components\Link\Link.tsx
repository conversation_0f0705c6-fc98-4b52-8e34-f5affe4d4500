import React, { CSSProperties, ReactEventHandler, ReactNode } from 'react';
import NextLink from 'next/link';
import classNames from '@utils/classNames';

interface LinkProps {
  id?: string;
  children?: ReactNode;
  href?: string;
  className?: string;
  style?: CSSProperties;
  isExternal?: boolean;
  onClick?: ReactEventHandler;
  scroll?: boolean;
  shallow?: boolean;
  ariaLabel?: string;
  dataID?: string;
  suppressHydrationWarning?: boolean;
}

export default function Link({
  id,
  children,
  href = '',
  className,
  style,
  isExternal = false,
  onClick,
  scroll = true,
  shallow = false,
  ariaLabel,
  dataID = null,
  suppressHydrationWarning = false,
}: LinkProps) {
  return isExternal ? (
    <a
      suppressHydrationWarning={suppressHydrationWarning}
      id={id}
      className={classNames(className)}
      href={href}
      style={style}
      target="_blank"
      rel="noreferrer"
      aria-label={ariaLabel}
      onClick={onClick}
      data-id={dataID}
    >
      {children}
    </a>
  ) : (
    <NextLink
      href={href}
      scroll={scroll}
      shallow={shallow}
      suppressHydrationWarning={suppressHydrationWarning}
      id={id}
      className={classNames(className)}
      style={style}
      onClick={onClick}
      data-id={dataID}
      aria-label={ariaLabel}
    >
      {children}
    </NextLink>
  );
}
