@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, brandColorThree, fifteenSpace, grayBorder, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md, breakpoint-xl-2000, breakpoint-sm-450, breakpoint-sm-550, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px from breakpoints;

.main_container_embla {
  padding: 80px 16px;
  background-color: gray300;

  @media screen and (max-width: breakpoint-md) {
    padding: 40px 0;
  }
}

.main_container {
  display: flex;
  justify-content: center;
  padding: 80px 16px;
  background-color: gray300;

  @media screen and (max-width: breakpoint-md) {
    padding: 40px 0;
  }
}

.inner_container {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.main_box {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 380px;
}

.heading>h2 {
  color: colorBlack;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
  text-align: center;
}

.card_box_container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  column-gap: 20px;
  row-gap: 40px;
  max-width: 1206px;
}

.single_image {
  position: relative;
  z-index: 1;
  width: 380px;
  height: 300px;
}

.title_description {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.text_icon_container {
  display: flex;

  flex-direction: column;

  align-self: stretch;

  gap: 10px;
  width: inherit;
}

.card_title>h3 {
  color: colorBlack;

  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 138%;
  letter-spacing: 0.48px;
}

.card_description {
  color: colorBlack;

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.imageWrapper {
  position: relative;
  width: 380px;
  height: 300px;
  border-radius: 6px;
}

.button_main_container {
  display: flex;
  gap: 4px;
  background-color: colorBlack;
  padding: 16px 24px;
  border-radius: 0 0 6px 6px;
  cursor: pointer;
}

.button_container {
  display: flex;
  border: none;
  background: none;
  color: #fff;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 23px;
}

.button_text_arrow_container {
  display: flex;
  gap: 4px;
}

.button_main_container:hover .button_text_arrow_container {
  .arrow_styling {
    display: none;
  }

  .arrow_styling_hover {
    display: flex;
    align-items: center;
  }

  transform: translateX(130%);

  transition: transform 0.4s ease;
}

.button_main_container .button_text_arrow_container {
  transition: transform 0.4s ease;
}

.button_main_container:not(:hover) .button_text_arrow_container {
  transform: translateX(0);
}

.arrow_container {
  display: flex;
  align-items: center;
  padding-top: 1px;
}

.arrow_styling {
  display: flex;
  align-items: center;
}

.arrow_styling_hover {
  display: none;
}

.embla {
  max-width: 100%;
  margin: auto;
  --slide-height: auto;
  --slide-spacing: 20px;
  --slide-size: auto;
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
  display: flex;
}

.embla__slide:first-child {
  margin-left: 9.375rem;

  @media (max-width: breakpoint-xl-1440) {
    margin-left: 7.75rem;
  }

  @media screen and (max-width: breakpoint-md) {
    margin-left: 2rem;
  }

  @media screen and (max-width: breakpoint-sm) {
    margin-left: 1rem;
  }
}

.embla__slide:last-child {
  margin-right: 9.375rem;

  @media (max-width: breakpoint-xl-1440) {
    margin-right: 7.75rem;
  }

  @media screen and (max-width: breakpoint-md) {
    margin-right: 2rem;
  }

  @media screen and (max-width: breakpoint-sm) {
    margin-right: 1rem;
  }
}

.embla__controls {
  display: grid;
  justify-content: center;
  gap: 1.2rem;
  margin-top: 40px;
}

.small_icon {
  width: fit-content;
}