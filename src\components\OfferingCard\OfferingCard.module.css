@value variables: "@styles/variables.module.css";
@value gray300, colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-md, breakpoint-md-767, breakpoint-md-769, breakpoint-lg, breakpoint-xl-1024, breakpoint-xl, breakpoint-xl-1309, breakpoint-xl-1400, breakpoint-xl-1440, breakpoint-sm-390, breakpoint-sm-550 from breakpoints;

.main_container {
  display: flex;
  justify-content: center;
  padding: 80px 120px;

  @media screen and (max-width: breakpoint-md) {
    padding: 40px 32px;
  }
}

.card_box {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.container {
  display: flex;
  flex-direction: column;
  gap: 40px;

  @media screen and (max-width: breakpoint-md) {
    gap: 0;
  }
}

.container_embla {
  padding: 5rem 9rem;
  background-color: gray300;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 0;
  }
}

.heading {
  color: colorBlack;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  /* 56px */
  letter-spacing: -0.8px;
  text-align: center;
}

.card__title {
  padding-bottom: 1rem;

  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 148%;
}

.card__description {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  transition: transform 0.3s ease;
}

.card {
  display: flex;
  padding: 40px 0;
  flex-direction: column;
  gap: 10px;
  width: 283px;
  border-radius: 6px;
  color: colorWhite;
  background-color: colorBlack;
  background-image: url(https://cdn.marutitech.com/Group_5043_dc21af6bd6.svg);
  background-repeat: no-repeat;
  padding: 1.5rem;
  text-align: left;
  user-select: none;

  @media screen and (max-width: breakpoint-md) {
    height: 380px;
  }
}

@media (min-width: breakpoint-md) {
  .card:hover {
    .card__description {
      transform: translateY(-10px);
    }
  }
}

.embla {
  max-width: 125rem;
  margin: auto;
  --slide-height: 19rem;
  --slide-spacing: 20px;
  --slide-size: 25%;

  @media (max-width: breakpoint-xl-1440) {
    max-width: 74.5rem;
    --slide-size: 25%;
  }

  @media (max-width: breakpoint-md-767) {
    max-width: auto;
    --slide-size: 100%;
  }

  @media (max-width: breakpoint-sm-390) {
    max-width: 320px;
    --slide-size: 100%;
  }
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  justify-content: space-between;
  gap: 10;
  touch-action: pan-y pinch-zoom;
  /* margin-left: calc(var(--slide-spacing) * -1); */
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding: 0 calc(var(--slide-spacing) / 2);
}

.embla__controls {
  display: grid;
  justify-content: center;
  gap: 1.2rem;
  margin-top: 40px;
}

.cardWrapper {
  color: colorWhite;
  padding: 40px 0;
}
