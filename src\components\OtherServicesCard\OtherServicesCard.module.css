@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, gray, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-md-767, breakpoint-md-769, breakpoint-lg, breakpoint-xl, breakpoint-xl-1400, breakpoint-sm-390, breakpoint-sm-550 from breakpoints;

.OtherServicesCardContainer {
  margin: 0;
  padding: 5rem 7.75rem;
  display: flex;
  flex-direction: column;
  gap: 40px;
  color: colorBlack;
  user-select: none;

  @media screen and (max-width: breakpoint-xl) {
    padding: 40px 32px;
  }

  @media (max-width: breakpoint-md-769) {
    padding: 5rem 0;
    gap: 30px;
  }
}

.l3OtherServicesCardContainer {
  background-color: colorBlack;
  color: white;
}

.title>h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;
  letter-spacing: 0.8px;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 28px;
    line-height: 38.64px;
    letter-spacing: 0.84px;
  }
}

.cardWrapper {
  display: flex;
  justify-content: center;
  gap: 20px;

  @media (max-width: breakpoint-sm) {
    text-align: center;
  }
}

.card {
  position: relative;
  max-width: 384px;
  display: block;
  height: 400px;
  padding: 1.5rem;
  text-decoration: none;
  color: colorWhite;
  border-radius: 6px;
  background-image: url("data:image/svg+xml,%3Csvg width='384' height='400' viewBox='0 0 384 400' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg opacity='0.5'%3E%3Cg filter='url(%23filter0_f_4598_2702)'%3E%3Cpath d='M303.908 137.522C308.618 96.0644 263.719 56.9215 203.623 50.0944C143.527 43.2673 78.6971 157.934 73.9874 199.392C69.2776 240.85 131.745 140.611 191.841 147.438C251.937 154.265 299.199 178.981 303.908 137.522Z' fill='%23EA5541'/%3E%3C/g%3E%3Cg filter='url(%23filter1_f_4598_2702)'%3E%3Cpath d='M128.334 117.016C110.892 136.552 109.895 151.462 101.192 174.362C84.3909 218.567 96.7647 247.597 76.7961 290.97C36.0514 379.471 -34.4063 100.888 65.81 49.9638C128.809 17.9514 313.155 10.2769 257.581 49.9638C235.387 65.8134 214.145 64.14 189.08 77.0403C163.212 90.3538 146.133 97.0806 128.334 117.016Z' fill='url(%23paint0_linear_4598_2702)'/%3E%3C/g%3E%3Cg filter='url(%23filter2_f_4598_2702)'%3E%3Cpath d='M285.16 158.048C257.293 155.78 239.061 143.722 221.261 115.784C185.201 59.1881 394.446 42.274 365.552 104.121C347.333 143.116 320.754 160.945 285.16 158.048Z' fill='%23F8B810' fill-opacity='0.5'/%3E%3C/g%3E%3Cg filter='url(%23filter3_f_4598_2702)'%3E%3Cpath d='M285.97 120.778C262.81 119.254 247.694 110.705 232.992 90.8148C203.207 50.5207 377.227 37.7207 352.992 81.9881C337.711 109.9 315.553 122.724 285.97 120.778Z' fill='%23ED7A37'/%3E%3C/g%3E%3C/g%3E%3Cdefs%3E%3Cfilter id='filter0_f_4598_2702' x='-38.5262' y='-62.4605' width='455.036' height='384.197' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeBlend mode='normal' in='SourceGraphic' in2='BackgroundImageFix' result='shape'/%3E%3CfeGaussianBlur stdDeviation='56.1314' result='effect1_foregroundBlur_4598_2702'/%3E%3C/filter%3E%3Cfilter id='filter1_f_4598_2702' x='-97.7615' y='-89.2627' width='477.911' height='509.731' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeBlend mode='normal' in='SourceGraphic' in2='BackgroundImageFix' result='shape'/%3E%3CfeGaussianBlur stdDeviation='56.1314' result='effect1_foregroundBlur_4598_2702'/%3E%3C/filter%3E%3Cfilter id='filter2_f_4598_2702' x='104.806' y='-47.3644' width='375.757' height='317.979' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeBlend mode='normal' in='SourceGraphic' in2='BackgroundImageFix' result='shape'/%3E%3CfeGaussianBlur stdDeviation='56.1314' result='effect1_foregroundBlur_4598_2702'/%3E%3C/filter%3E%3Cfilter id='filter3_f_4598_2702' x='117.288' y='-58.0829' width='350.29' height='291.316' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeBlend mode='normal' in='SourceGraphic' in2='BackgroundImageFix' result='shape'/%3E%3CfeGaussianBlur stdDeviation='56.1314' result='effect1_foregroundBlur_4598_2702'/%3E%3C/filter%3E%3ClinearGradient id='paint0_linear_4598_2702' x1='93.9214' y1='209.868' x2='135.355' y2='57.9525' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%23AE1F5D'/%3E%3Cstop offset='1' stop-color='%23D31A5E'/%3E%3C/linearGradient%3E%3C/defs%3E%3C/svg%3E%0A");
  background-color: colorBlack;
  align-content: end;

  @media (max-width: breakpoint-sm) {
    font-size: 28px;
    font-style: normal;
    font-weight: 600;
    line-height: 138%;
    letter-spacing: -0.84px;
  }

  @media (max-width: breakpoint-sm-390) {
    width: 300px;
    height: 400px;
  }

  background-size: 100% 100%;
}

.onHoverBgImage {
  visibility: hidden;
  opacity: 0;
  transition:
    opacity 0.4s ease-in-out,
    visibility 0.4s ease-in-out;
  border: 0;
  border-radius: 6px;
  object-fit: cover;

  @media (max-width: 769px) {
    opacity: 1;
    visibility: visible;
  }
}

.card:hover .onHoverBgImage {
  opacity: 1;
  visibility: visible;
}

.contentWrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.cardOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(40deg,
      rgba(0, 0, 0, 0.7) 40%,
      rgba(0, 0, 0, 0) 75%);
  opacity: 0;
  transition: opacity 0.5s ease-out;
  z-index: 1;
  border: 0;
  border-radius: 6px;

  @media (max-width: 769px) {
    opacity: 1;
  }
}

.card:hover .cardOverlay {
  opacity: 1;
}

.cardTitle>h3 {
  position: relative;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 138%;
  letter-spacing: 0.48px;
  z-index: 2;
}

.cardDescription {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  z-index: 2;
  line-height: normal;

  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.allServicesCardWrapper {
  width: 384px;
  height: 400px;
  text-decoration: none;
  color: colorWhite;

  transition: all 0.5s ease;
  display: table;
  align-content: center;
  text-align: center;
  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  letter-spacing: -0.8px;

  @media (max-width: breakpoint-sm-390) {
    width: 300px;
    height: 400px;
  }
}

.allServicesCard {
  padding: 0 20px;

  position: relative;
  background-color: colorBlack;
  cursor: pointer;
  color: colorWhite;
  transition: 0.2s linear;
  text-align: center;
  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  letter-spacing: -0.8px;
  display: table-cell;
  vertical-align: middle;
  border-radius: 6px;

  @media (max-width: breakpoint-sm) {
    font-size: 28px;
    font-style: normal;
    font-weight: 600;
    line-height: 138%;
    letter-spacing: -0.84px;
  }
}

.allServicesCard::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  opacity: 0;
  border-radius: 6px;
  padding: 3.75px;
  /* This is the width of the border */
  transition: opacity 0.5s ease-in-out;
  -webkit-mask:
    linear-gradient(colorWhite 0 0) content-box,
    linear-gradient(colorWhite 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
}

.allServicesCard:hover::before {
  opacity: 1;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
}

.embla {
  max-width: 48rem;
  margin: auto;
  --slide-height: 19rem;
  --slide-spacing: 1rem;
  --slide-size: 55%;

  @media screen and (max-width: breakpoint-xl) {
    max-width: 44rem;
    --slide-size: 47%;
  }

  @media (max-width: breakpoint-md-767) {
    max-width: 35rem;
    --slide-size: 70%;
  }

  @media (max-width: breakpoint-sm-550) {
    max-width: 384px;
    --slide-size: 100%;
  }

  @media (max-width: breakpoint-sm-390) {
    max-width: 300px;
    --slide-size: 100%;
  }
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  justify-content: space-between;
  gap: 10;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
}

.embla__controls {
  display: grid;
  justify-content: center;
  gap: 1.2rem;
  margin-top: 40px;
}

.submitButton {
  padding: 16px 36px !important;

  @media (max-width: breakpoint-md-767) {
    width: 100%;
  }
}

/* L3 Other services card variant */
.emblaL3 {
  max-width: 48rem;
  margin: auto;
  --slide-height: 19rem;
  --slide-spacing: 1rem;
  --slide-size: 50%;

  @media (min-width: breakpoint-xl-1400) {
    max-width: 1232px;
  }

  @media (max-width: breakpoint-lg) {
    max-width: 35rem;
    --slide-size: 70%;
  }

  @media (max-width: breakpoint-sm-550) {
    max-width: 384px;
    --slide-size: 100%;
  }

  @media (max-width: breakpoint-sm-390) {
    max-width: 300px;
    --slide-size: 100%;
  }
}

.embla__viewport__l3 {
  overflow: hidden;
}

.embla__container__l3 {
  backface-visibility: hidden;
  display: flex;
  justify-content: space-between;
  /* gap: 20px; */
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.embla__slide__l3 {
  max-width: 384px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: var(--slide-spacing);
}

.embla__dots__l3 {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
  outline: none;
  border: 0;
}