@value variables: "@styles/variables.module.css";
@value gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-xl, breakpoint-lg, breakpoint-md, breakpoint-sm from breakpoints;

.containerPodcastsLinks {
  padding: 5rem 9.375rem;
  display: flex;
  flex-direction: column;
  gap: 40px;

  @media (max-width: breakpoint-xl-1440) {
    padding: 5rem 7.75rem;
  }

  @media (max-width: breakpoint-md) {
    padding: 2.5rem 2rem;
  }

  @media (max-width: breakpoint-sm) {
    padding: 2.5rem 1rem;
  }
}

.title > h2 {
  justify-self: center;
  font-size: 40px;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
}

.linksWrapper {
  display: flex;
  gap: 100px;
  justify-content: center;
  align-items: center;

  @media (max-width: breakpoint-xl-1440) {
    gap: 0;
    justify-content: space-around;
  }

  @media (max-width: breakpoint-lg) {
    flex-direction: column;
    gap: 40px;
  }
}

.linksWrapper > a > img {
  filter: grayscale(100%);

  @media (max-width: breakpoint-lg) {
    filter: grayscale(0%);
  }
}

.linksWrapper > a:hover > img {
  filter: grayscale(0%);
  transition: filter 0.3s ease;
}
