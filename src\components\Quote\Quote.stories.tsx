import Quote from './Quote';

export default {
  title: 'Components/Quote',
};

const data = {
  data: [
    {
      id: 1,
      attributes: {
        createdAt: '2024-08-12T12:50:27.050Z',
        updatedAt: '2024-09-09T08:46:44.410Z',
        publishedAt: '2024-08-13T12:51:47.373Z',
        title: 'Overhauling a High-Performance Property Listing Platform',
        slug: 'first-case-study',
        quote: {
          id: 1,
          rich_text:
            '<p style="text-align:center;"><span style="font-size:26px;"><i>"The team tests the code before presenting it to us so that by the time I see it, it’s almost always functioning perfectly. Further, they’ve repaired mistakes made by previous developers."</i></span></p>',
          quote_by: '- CEO, Buzzz Media.',
        },
      },
    },
  ],
  meta: {
    pagination: {
      page: 1,
      pageSize: 25,
      pageCount: 1,
      total: 1,
    },
  },
};

export function QuoteStory() {
  return (
    <div>
      <Quote QuoteData={data?.data[0]?.attributes?.quote} />
    </div>
  );
}
