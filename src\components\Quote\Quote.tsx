import React from 'react';
import { Container } from 'react-bootstrap';
import styles from './Quote.module.css';

interface QuoteTypes {
  QuoteData: {
    rich_text: string;
    quote_by?: string;
  };
}

export default function Quote({ QuoteData }: QuoteTypes) {
  return (
    <Container fluid className={styles.container}>
      <div
        dangerouslySetInnerHTML={{
          __html: QuoteData.rich_text,
        }}
      ></div>
      <div className={styles.title}>{QuoteData?.quote_by}</div>
    </Container>
  );
}
