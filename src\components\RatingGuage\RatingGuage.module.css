@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, gray300, colorWhite from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024, breakpoint-md from breakpoints;

.gauge_container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  width: 100%;
  max-width: 500px;
}

.result_text {
  text-align: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%;
  color: colorWhite;
  padding: 5px 10px;
  width: fit-content;
  border-radius: 6px;
}

.gauge {
  width: 100% !important;
}
