'use client';

import React from 'react';

const RatingProgressCircle = ({
  percentage = 50,
  radius = 20,
  strokeWidth = 7,
}) => {
  const normalizedRadius = radius - strokeWidth / 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <svg height={radius * 2} width={radius * 2}>
      <defs>
        <linearGradient id="gradient" x1="0" y1="0" x2="1" y2="0.15">
          <stop offset="0%" stopColor="#febe10" />
          <stop offset="30.56%" stopColor="#f47a37" />
          <stop offset="53.47%" stopColor="#f05443" />
          <stop offset="75.75%" stopColor="#d91a5f" />
          <stop offset="100%" stopColor="#b41f5e" />
        </linearGradient>
      </defs>

      {/* Background circle */}
      <circle
        stroke="#ddd"
        fill="transparent"
        strokeWidth={strokeWidth}
        r={normalizedRadius}
        cx={radius}
        cy={radius}
      />

      {/* Progress circle */}
      <circle
        stroke="url(#gradient)"
        fill="transparent"
        strokeWidth={strokeWidth}
        strokeDasharray={circumference}
        strokeDashoffset={strokeDashoffset}
        strokeLinecap="round"
        r={normalizedRadius}
        cx={radius}
        cy={radius}
        transform={`rotate(-90 ${radius} ${radius})`} // Rotate to start from top
        style={{ transition: 'stroke-dashoffset 0.5s ease' }}
      />
    </svg>
  );
};

export default RatingProgressCircle;
