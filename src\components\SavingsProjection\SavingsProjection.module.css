.savings_container {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.savings_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
}

.savings_header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.timeframe_selector {
  display: flex;
  background: #f1f3f4;
  border-radius: 8px;
  padding: 4px;
}

.timeframe_selector button {
  padding: 8px 16px;
  border: none;
  background: transparent;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
}

.timeframe_selector button.active {
  background: #28a745;
  color: white;
}

.timeframe_selector button:hover:not(.active) {
  background: #e9ecef;
}

.savings_content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* ROI Summary */
.roi_summary {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.roi_card {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  min-width: 200px;
}

.roi_value {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.roi_label {
  font-size: 1rem;
  opacity: 0.9;
}

.cost_comparison {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.cost_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #dee2e6;
}

.cost_label {
  font-weight: 500;
  color: #495057;
}

.cost_value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
}

.cost_value.positive {
  color: #28a745;
}

.cost_value.negative {
  color: #dc3545;
}

/* Savings Breakdown */
.savings_breakdown h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 20px;
}

.breakdown_items {
  display: grid;
  gap: 15px;
}

.breakdown_item {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.breakdown_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.breakdown_header span:first-child {
  font-weight: 600;
  color: #1a1a1a;
}

.breakdown_header span:last-child {
  font-weight: 600;
  color: #28a745;
  font-size: 1.1rem;
}

.breakdown_description {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

/* Timeline Chart */
.timeline_chart h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 20px;
}

.chart_wrapper {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 120px;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.timeline_year {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.year_label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #666;
}

.year_bars {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.cost_bar,
.savings_bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.bar_fill {
  width: 20px;
  border-radius: 2px 2px 0 0;
  min-height: 5px;
  transition: height 0.8s ease;
}

.cost_bar span,
.savings_bar span {
  font-size: 0.75rem;
  font-weight: 500;
  color: #666;
  white-space: nowrap;
}

.chart_legend {
  display: flex;
  justify-content: center;
  gap: 30px;
}

.legend_item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend_color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend_item span {
  font-size: 0.9rem;
  color: #666;
}

/* Disclaimer */
.disclaimer {
  margin-top: 30px;
  padding: 20px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
}

.disclaimer p {
  margin: 0;
  font-size: 0.9rem;
  color: #856404;
  line-height: 1.5;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .savings_container {
    padding: 20px;
  }

  .savings_header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .savings_header h3 {
    font-size: 1.3rem;
    text-align: center;
  }

  .timeframe_selector {
    justify-content: center;
  }

  .roi_summary {
    flex-direction: column;
    gap: 20px;
  }

  .roi_card {
    min-width: auto;
  }

  .roi_value {
    font-size: 2rem;
  }

  .cost_item {
    padding: 12px 15px;
  }

  .breakdown_header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .chart_wrapper {
    height: 100px;
    padding: 15px;
  }

  .year_bars {
    gap: 5px;
  }

  .bar_fill {
    width: 15px;
  }

  .chart_legend {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .savings_container {
    padding: 15px;
  }

  .roi_value {
    font-size: 1.8rem;
  }

  .cost_value {
    font-size: 1rem;
  }

  .breakdown_item {
    padding: 15px;
  }

  .chart_wrapper {
    height: 80px;
    padding: 10px;
  }

  .bar_fill {
    width: 12px;
  }

  .cost_bar span,
  .savings_bar span {
    font-size: 0.7rem;
  }

  .chart_legend {
    flex-direction: column;
    gap: 10px;
    align-items: center;
  }
}
