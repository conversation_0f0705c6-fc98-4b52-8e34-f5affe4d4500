'use client';

import { useState } from 'react';
import styles from './SavingsProjection.module.css';

interface SavingsProjectionProps {
  costResults: {
    totalMonthlyCost: number;
    totalOneTimeCost: number;
    annualCost: number;
    threeYearCost: number;
  };
}

export default function SavingsProjection({ costResults }: SavingsProjectionProps) {
  const [selectedTimeframe, setSelectedTimeframe] = useState<'1year' | '3year' | '5year'>('3year');

  // Estimated savings percentages (these could come from Strapi)
  const savingsData = {
    infrastructure: 0.25, // 25% savings on infrastructure
    operational: 0.30,    // 30% savings on operational costs
    maintenance: 0.40,    // 40% savings on maintenance
    productivity: 0.20,   // 20% productivity gains
  };

  const calculateSavings = (timeframe: string) => {
    const years = timeframe === '1year' ? 1 : timeframe === '3year' ? 3 : 5;
    const totalCost = costResults.annualCost * years + costResults.totalOneTimeCost;

    // Calculate different types of savings
    const infrastructureSavings = totalCost * 0.4 * savingsData.infrastructure * years;
    const operationalSavings = totalCost * 0.3 * savingsData.operational * years;
    const maintenanceSavings = totalCost * 0.2 * savingsData.maintenance * years;
    const productivityGains = totalCost * 0.1 * savingsData.productivity * years;

    const totalSavings = infrastructureSavings + operationalSavings + maintenanceSavings + productivityGains;
    const roi = ((totalSavings - totalCost) / totalCost) * 100;

    return {
      totalCost,
      totalSavings,
      netSavings: totalSavings - totalCost,
      roi,
      breakdown: {
        infrastructure: infrastructureSavings,
        operational: operationalSavings,
        maintenance: maintenanceSavings,
        productivity: productivityGains,
      },
    };
  };

  const currentProjection = calculateSavings(selectedTimeframe);

  const timeframes = [
    { key: '1year', label: '1 Year', years: 1 },
    { key: '3year', label: '3 Years', years: 3 },
    { key: '5year', label: '5 Years', years: 5 },
  ];

  return (
    <div className={styles.savings_container}>
      <div className={styles.savings_header}>
        <h3>Projected Savings & ROI</h3>
        <div className={styles.timeframe_selector}>
          {timeframes.map((timeframe) => (
            <button
              key={timeframe.key}
              className={selectedTimeframe === timeframe.key ? styles.active : ''}
              onClick={() => setSelectedTimeframe(timeframe.key as any)}
            >
              {timeframe.label}
            </button>
          ))}
        </div>
      </div>

      <div className={styles.savings_content}>
        {/* ROI Summary */}
        <div className={styles.roi_summary}>
          <div className={styles.roi_card}>
            <div className={styles.roi_value}>
              {currentProjection.roi > 0 ? '+' : ''}{currentProjection.roi.toFixed(1)}%
            </div>
            <div className={styles.roi_label}>Return on Investment</div>
          </div>

          <div className={styles.cost_comparison}>
            <div className={styles.cost_item}>
              <span className={styles.cost_label}>Migration Cost</span>
              <span className={styles.cost_value}>
                ${currentProjection.totalCost.toLocaleString()}
              </span>
            </div>
            <div className={styles.cost_item}>
              <span className={styles.cost_label}>Projected Savings</span>
              <span className={`${styles.cost_value} ${styles.positive}`}>
                ${currentProjection.totalSavings.toLocaleString()}
              </span>
            </div>
            <div className={styles.cost_item}>
              <span className={styles.cost_label}>Net Benefit</span>
              <span className={`${styles.cost_value} ${currentProjection.netSavings > 0 ? styles.positive : styles.negative}`}>
                ${Math.abs(currentProjection.netSavings).toLocaleString()}
              </span>
            </div>
          </div>
        </div>

        {/* Savings Breakdown */}
        <div className={styles.savings_breakdown}>
          <h4>Savings Breakdown</h4>
          <div className={styles.breakdown_items}>
            <div className={styles.breakdown_item}>
              <div className={styles.breakdown_header}>
                <span>Infrastructure Optimization</span>
                <span>${currentProjection.breakdown.infrastructure.toLocaleString()}</span>
              </div>
              <div className={styles.breakdown_description}>
                Reduced hardware costs, optimized resource utilization
              </div>
            </div>

            <div className={styles.breakdown_item}>
              <div className={styles.breakdown_header}>
                <span>Operational Efficiency</span>
                <span>${currentProjection.breakdown.operational.toLocaleString()}</span>
              </div>
              <div className={styles.breakdown_description}>
                Automated processes, reduced manual intervention
              </div>
            </div>

            <div className={styles.breakdown_item}>
              <div className={styles.breakdown_header}>
                <span>Maintenance Reduction</span>
                <span>${currentProjection.breakdown.maintenance.toLocaleString()}</span>
              </div>
              <div className={styles.breakdown_description}>
                Lower maintenance overhead, managed services
              </div>
            </div>

            <div className={styles.breakdown_item}>
              <div className={styles.breakdown_header}>
                <span>Productivity Gains</span>
                <span>${currentProjection.breakdown.productivity.toLocaleString()}</span>
              </div>
              <div className={styles.breakdown_description}>
                Improved performance, faster deployment cycles
              </div>
            </div>
          </div>
        </div>

        {/* Timeline Chart */}
        <div className={styles.timeline_chart}>
          <h4>Cost vs Savings Timeline</h4>
          <div className={styles.chart_wrapper}>
            {Array.from({ length: parseInt(selectedTimeframe.charAt(0)) }, (_, index) => {
              const year = index + 1;
              const yearCost = costResults.annualCost + (index === 0 ? costResults.totalOneTimeCost : 0);
              const yearSavings = currentProjection.totalSavings / parseInt(selectedTimeframe.charAt(0));
              const maxValue = Math.max(yearCost, yearSavings);

              return (
                <div key={year} className={styles.timeline_year}>
                  <div className={styles.year_label}>Year {year}</div>
                  <div className={styles.year_bars}>
                    <div className={styles.cost_bar}>
                      <div
                        className={styles.bar_fill}
                        style={{
                          height: `${(yearCost / maxValue) * 80}px`,
                          backgroundColor: '#ff6b6b'
                        }}
                      />
                      <span>${(yearCost / 1000).toFixed(0)}K</span>
                    </div>
                    <div className={styles.savings_bar}>
                      <div
                        className={styles.bar_fill}
                        style={{
                          height: `${(yearSavings / maxValue) * 80}px`,
                          backgroundColor: '#4ecdc4'
                        }}
                      />
                      <span>${(yearSavings / 1000).toFixed(0)}K</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
          <div className={styles.chart_legend}>
            <div className={styles.legend_item}>
              <div className={styles.legend_color} style={{ backgroundColor: '#ff6b6b' }} />
              <span>Annual Costs</span>
            </div>
            <div className={styles.legend_item}>
              <div className={styles.legend_color} style={{ backgroundColor: '#4ecdc4' }} />
              <span>Annual Savings</span>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.disclaimer}>
        <p>
          <strong>Note:</strong> These projections are estimates based on industry averages and your inputs.
          Actual savings may vary depending on implementation, usage patterns, and specific business requirements.
        </p>
      </div>
    </div >
  );
}
