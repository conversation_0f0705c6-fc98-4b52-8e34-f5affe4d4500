import ServiceDeliveryProcess from './ServiceDeliveryProcess';
import { ServiceDeliveryProcessTypes } from './types';

export default {
  title: 'Components/ServiceDeliveryProcess',
};

const data: ServiceDeliveryProcessTypes = {
  data: {
    id: 1,
    title: 'Looking for Other Services',
    other_services_card: [
      {
        id: 1,
        title: 'Quality Engineering',
        description:
          '<p>Retail is shifting gradually from brick &amp; mortar shops to online stores.</p>',
        service_page_link: '#',
        on_hover_bg_image: {
          data: {
            id: 51,
            attributes: {
              name: 'service1.jpg',
              alternativeText: null,
              caption: null,
              width: 784,
              height: 522,
              hash: 'service1_e0695fa866',
              ext: '.jpg',
              mime: 'image/jpeg',
              size: 63.72,
              url: 'https://cdn.marutitech.com/service1_e0695fa866.jpg',
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-06-19T12:26:28.513Z',
              updatedAt: '2024-06-27T06:06:05.255Z',
            },
          },
        },
      },
      {
        id: 2,
        title: 'Quality Engineering 2',
        description:
          '<p>Retail is shifting gradually from brick &amp; mortar shops to online stores.</p>',
        service_page_link: '#',
        on_hover_bg_image: {
          data: {
            id: 62,
            attributes: {
              name: 'l2_hero_section.png',
              alternativeText: null,
              caption: null,
              width: 1440,
              formats: null,
              height: 810,
              hash: 'l2_hero_section_aaf94de4c2',
              ext: '.png',
              mime: 'image/png',
              size: 358.94,
              url: 'https://cdn.marutitech.com/l2_hero_section_aaf94de4c2.png',
              previewUrl: null,
              provider:
                '@strapi-community/strapi-provider-upload-google-cloud-storage',
              provider_metadata: null,
              createdAt: '2024-07-05T08:55:04.726Z',
              updatedAt: '2024-07-11T10:19:05.401Z',
            },
          },
        },
      },
    ],
    all_services_card: {
      id: 5,
      title: 'Explore All Services',
      link: '#',
    },
  },
};

export function ServiceDeliveryProcessStory() {
  return (
    <div>
      <ServiceDeliveryProcess data={{ ...data.data }} />
    </div>
  );
}
