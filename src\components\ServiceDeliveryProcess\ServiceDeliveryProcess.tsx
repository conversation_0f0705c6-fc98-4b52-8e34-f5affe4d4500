'use client';

import React from 'react';
import { Container } from 'react-bootstrap';
import useEmblaCarousel from 'embla-carousel-react';
import DotButton from '@components/DotButton/DotButton';
import Heading from '@components/Heading';
import useDotButton from '@hooks/useDotButton';
import styles from './ServiceDeliveryProcess.module.css';
import emblastyles from '../../styles/emlaDots.module.css';
import classNames from '@utils/classNames';

type cardsTypes = {
  number: number;
  title: string;
  description: string;
};

export default function ServiceDeliveryProcess({ data, variantWhite = true }) {
  const [emblaRef, emblaApi] = useEmblaCarousel({ dragFree: true });

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  return (
    <Container fluid className={styles.container}>
      <Heading
        headingType="h2"
        title={data?.title}
        position="center"
        className={styles.heading}
      />
      <div className={styles.embla}>
        <div className={styles.embla__viewport} ref={emblaRef}>
          <div className={styles.embla__container}>
            {data?.cards.map((data: cardsTypes, index: number) => (
              <div className={styles.cardWrapper} key={index}>
                <div className={styles.embla__slide} key={1}>
                  <div className={styles.card} key={index}>
                    <div className={styles.cardTop}>
                      <div className={styles.card__number}>
                        {data?.number || index + 1}
                      </div>
                      <Heading
                        headingType="h3"
                        title={data?.title}
                        className={styles.card__title}
                      />
                    </div>
                    <div
                      className={styles.card__description}
                      dangerouslySetInnerHTML={{
                        __html: data?.description,
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className={styles.embla__controls}>
        <div className={emblastyles.embla__dots}>
          {scrollSnaps.length > 1 &&
            scrollSnaps.map((_, index) => (
              <DotButton
                key={index}
                onClick={() => onDotButtonClick(index)}
                className={
                  index === selectedIndex
                    ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                    : variantWhite
                      ? classNames(
                          emblastyles.embla__dot,
                          emblastyles.embla__dot_bg_white,
                        )
                      : emblastyles.embla__dot
                }
              />
            ))}
        </div>
      </div>
    </Container>
  );
}
