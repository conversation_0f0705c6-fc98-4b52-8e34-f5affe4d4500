import ServicesCard from './ServicesCard';
import { L2ServiceType, ServicesCardTypes } from './types';

export default {
  title: 'Components/Our Services Card',
};

const servicesData: ServicesCardTypes = {
  id: 1,
  title: 'Our services',
  subtitle:
    '<p>Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities.</p>',
  ourServicesCard: [
    {
      id: 1,
      cardTitle: 'Software Product  Development',
      cardContent:
        '<p>Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities. Delivering domain expertise and technology-driven offerings to help you.</p>',
      cardTitle2: 'Software Product  Development',
      url: '#',
      cardImage: {
        data: {
          id: 51,
          attributes: {
            name: 'service1.jpg',
            alternativeText: null,
            caption: null,
            width: 784,
            height: 522,
            formats: null,
            hash: 'service1_e0695fa866',
            ext: '.jpg',
            mime: 'image/jpeg',
            size: 63.72,
            url: 'https://cdn.marutitech.com/service1_e0695fa866.jpg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-06-19T12:26:28.513Z',
            updatedAt: '2024-06-19T12:26:28.513Z',
          },
        },
      },
    },
    {
      id: 2,
      cardTitle: 'Software Product  Development',
      cardContent:
        '<p>Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities. Delivering domain expertise and technology-driven offerings to help you.</p>',
      cardTitle2: 'Software Product  Development',
      url: '#',
      cardImage: {
        data: {
          id: 47,
          attributes: {
            name: 'service2.jpg',
            alternativeText: null,
            caption: null,
            width: 784,
            height: 538,
            formats: null,
            hash: 'service2_81b42df2df',
            ext: '.jpg',
            mime: 'image/jpeg',
            size: 97.21,
            url: 'https://cdn.marutitech.com/service2_81b42df2df.jpg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-06-19T12:26:28.243Z',
            updatedAt: '2024-06-19T12:26:28.243Z',
          },
        },
      },
    },
    {
      id: 3,
      cardTitle: 'Software Product  Development',
      cardContent:
        '<p>Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities. Delivering domain expertise and technology-driven offerings to help you.</p>',
      cardTitle2: 'Software Product  Development',
      url: '#',
      cardImage: {
        data: {
          id: 48,
          attributes: {
            name: 'service3.jpg',
            alternativeText: null,
            caption: null,
            width: 469,
            height: 784,
            formats: null,
            hash: 'service3_e9782f8133',
            ext: '.jpg',
            mime: 'image/jpeg',
            size: 56.53,
            url: 'https://cdn.marutitech.com/service3_e9782f8133.jpg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-06-19T12:26:28.264Z',
            updatedAt: '2024-06-19T12:26:28.264Z',
          },
        },
      },
    },
    {
      id: 4,
      cardTitle: 'Software Product  Development',
      cardContent:
        '<p>Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities. Delivering domain expertise and technology-driven offerings to help you.</p>',
      cardTitle2: 'Software Product  Development',
      url: '#',
      cardImage: {
        data: {
          id: 49,
          attributes: {
            name: 'service5.jpg',
            alternativeText: null,
            caption: null,
            width: 522,
            height: 784,
            formats: null,
            hash: 'service5_a115044b8d',
            ext: '.jpg',
            mime: 'image/jpeg',
            size: 70.47,
            url: 'https://cdn.marutitech.com/service5_a115044b8d.jpg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-06-19T12:26:28.337Z',
            updatedAt: '2024-06-19T12:26:28.337Z',
          },
        },
      },
    },
    {
      id: 5,
      cardTitle: 'Software Product  Development',
      cardContent:
        '<p>Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities. Delivering domain expertise and technology-driven offerings to help you.</p>',
      cardTitle2: 'Software Product  Development',
      url: '#',
      cardImage: {
        data: {
          id: 47,
          attributes: {
            name: 'service2.jpg',
            alternativeText: null,
            caption: null,
            width: 784,
            height: 538,
            formats: null,
            hash: 'service2_81b42df2df',
            ext: '.jpg',
            mime: 'image/jpeg',
            size: 97.21,
            url: 'https://cdn.marutitech.com/service2_81b42df2df.jpg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-06-19T12:26:28.243Z',
            updatedAt: '2024-06-19T12:26:28.243Z',
          },
        },
      },
    },
  ],
};

const l2ServiceCardData: L2ServiceType = {
  id: 2,
  title: 'What Service we are offering',
  L2ServicesCard: [
    {
      id: 11,
      title: 'Enterprise Application Modernization',
      description:
        '<p>Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities. Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities.</p>',
      service_page_link: '/#',
      on_hover_bg_image: {
        data: {
          id: 64,
          attributes: {
            name: 'Group 5042.svg',
            alternativeText: null,
            caption: null,
            width: 384,
            height: 401,
            formats: null,
            hash: 'Group_5042_f42340bd64',
            ext: '.svg',
            mime: 'image/svg+xml',
            size: 3.07,
            url: 'https://cdn.marutitech.com/Group_5042_f42340bd64.svg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-07-11T10:47:14.941Z',
            updatedAt: '2024-07-11T10:47:14.941Z',
          },
        },
      },
    },
    {
      id: 12,
      title: 'Enterprise Application Modernization',
      description:
        '<p>Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities. Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities.</p>',
      service_page_link: '/#',
      on_hover_bg_image: {
        data: {
          id: 64,
          attributes: {
            name: 'Group 5042.svg',
            alternativeText: null,
            caption: null,
            width: 384,
            height: 401,
            formats: null,
            hash: 'Group_5042_f42340bd64',
            ext: '.svg',
            mime: 'image/svg+xml',
            size: 3.07,
            url: 'https://cdn.marutitech.com/Group_5042_f42340bd64.svg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-07-11T10:47:14.941Z',
            updatedAt: '2024-07-11T10:47:14.941Z',
          },
        },
      },
    },
    {
      id: 13,
      title: 'Enterprise Application Modernization',
      description:
        '<p>Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities. Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities.</p>',
      service_page_link: '/#',
      on_hover_bg_image: {
        data: {
          id: 64,
          attributes: {
            name: 'Group 5042.svg',
            alternativeText: null,
            caption: null,
            width: 384,
            height: 401,
            formats: null,
            hash: 'Group_5042_f42340bd64',
            ext: '.svg',
            mime: 'image/svg+xml',
            size: 3.07,
            url: 'https://cdn.marutitech.com/Group_5042_f42340bd64.svg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-07-11T10:47:14.941Z',
            updatedAt: '2024-07-11T10:47:14.941Z',
          },
        },
      },
    },
    {
      id: 14,
      title: 'Enterprise Application Modernization',
      description:
        '<p>Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities. Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities.</p>',
      service_page_link: '/#',
      on_hover_bg_image: {
        data: {
          id: 64,
          attributes: {
            name: 'Group 5042.svg',
            alternativeText: null,
            caption: null,
            width: 384,
            height: 401,
            formats: null,
            hash: 'Group_5042_f42340bd64',
            ext: '.svg',
            mime: 'image/svg+xml',
            size: 3.07,
            url: 'https://cdn.marutitech.com/Group_5042_f42340bd64.svg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-07-11T10:47:14.941Z',
            updatedAt: '2024-07-11T10:47:14.941Z',
          },
        },
      },
    },
    {
      id: 15,
      title: 'Enterprise Application Modernization',
      description:
        '<p>Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities. Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities.</p>',
      service_page_link: '/#',
      on_hover_bg_image: {
        data: {
          id: 64,
          attributes: {
            name: 'Group 5042.svg',
            alternativeText: null,
            caption: null,
            width: 384,
            height: 401,
            formats: null,
            hash: 'Group_5042_f42340bd64',
            ext: '.svg',
            mime: 'image/svg+xml',
            size: 3.07,
            url: 'https://cdn.marutitech.com/Group_5042_f42340bd64.svg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-07-11T10:47:14.941Z',
            updatedAt: '2024-07-11T10:47:14.941Z',
          },
        },
      },
    },
    {
      id: 16,
      title: 'Enterprise Application Modernization',
      description:
        '<p>Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities. Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities.</p>',
      service_page_link: '/#',
      on_hover_bg_image: {
        data: {
          id: 64,
          attributes: {
            name: 'Group 5042.svg',
            alternativeText: null,
            caption: null,
            width: 384,
            height: 401,
            formats: null,
            hash: 'Group_5042_f42340bd64',
            ext: '.svg',
            mime: 'image/svg+xml',
            size: 3.07,
            url: 'https://cdn.marutitech.com/Group_5042_f42340bd64.svg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-07-11T10:47:14.941Z',
            updatedAt: '2024-07-11T10:47:14.941Z',
          },
        },
      },
    },
    {
      id: 17,
      title: 'Enterprise Application Modernization',
      description:
        '<p>Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities. Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities.</p>',
      service_page_link: '/#',
      on_hover_bg_image: {
        data: {
          id: 64,
          attributes: {
            name: 'Group 5042.svg',
            alternativeText: null,
            caption: null,
            width: 384,
            height: 401,
            formats: null,
            hash: 'Group_5042_f42340bd64',
            ext: '.svg',
            mime: 'image/svg+xml',
            size: 3.07,
            url: 'https://cdn.marutitech.com/Group_5042_f42340bd64.svg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-07-11T10:47:14.941Z',
            updatedAt: '2024-07-11T10:47:14.941Z',
          },
        },
      },
    },
    {
      id: 18,
      title: 'Enterprise Application Modernization',
      description:
        '<p>Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities. Delivering domain expertise and technology-driven offerings to help you turn digital challenges into opportunities.</p>',
      service_page_link: '/#',
      on_hover_bg_image: {
        data: {
          id: 64,
          attributes: {
            name: 'Group 5042.svg',
            alternativeText: null,
            caption: null,
            width: 384,
            height: 401,
            formats: null,
            hash: 'Group_5042_f42340bd64',
            ext: '.svg',
            mime: 'image/svg+xml',
            size: 3.07,
            url: 'https://cdn.marutitech.com/Group_5042_f42340bd64.svg',
            previewUrl: null,
            provider:
              '@strapi-community/strapi-provider-upload-google-cloud-storage',
            provider_metadata: null,
            createdAt: '2024-07-11T10:47:14.941Z',
            updatedAt: '2024-07-11T10:47:14.941Z',
          },
        },
      },
    },
  ],
};

export function OurServicesStory() {
  return (
    <>
      <ServicesCard data={servicesData} variant="showTextOnHover" />
      <ServicesCard
        l2ServiceData={l2ServiceCardData}
        variant="blackSlideCard"
      />
    </>
  );
}
