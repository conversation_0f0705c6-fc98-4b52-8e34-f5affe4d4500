import React from 'react';
import classNames from '@utils/classNames';

import styles from './SliderButtons.module.css';

// type PropType = ComponentPropsWithRef<'button'>;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function PrevButton(props: any) {
  const { ...restProps } = props;

  return (
    <button
      aria-label="carousel-prev-button"
      className={classNames(
        styles.embla__button,
        styles['embla__button--prev'],
      )}
      type="button"
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...restProps}
    >
      <svg
        className={styles.embla__button__svg}
        viewBox="0 0 40 40"
        fill="none"
        height={40}
        width={40}
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M31.6666 20H8.33325"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M19.9999 31.6666L8.33325 20L19.9999 8.33331"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </button>
  );
}
export default PrevButton;
