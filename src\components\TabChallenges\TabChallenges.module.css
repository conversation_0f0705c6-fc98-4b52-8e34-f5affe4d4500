@value variables: "@styles/variables.module.css";
@value gray300, colorBlack, colorWhite, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, grayBorder from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1365, breakpoint-md, breakpoint-sm-450, breakpoint-sm-550 from breakpoints;

.main_container {
  padding: 80px 124px;
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: center;

  @media screen and (min-width: 800px) and (max-width: 1399px) {
    padding: 80px 5px;
  }

  @media screen and (max-width: breakpoint-md) {
    padding: 40px 32px;
  }

  @media screen and (max-width: 1082px) {
    padding: 40px 32px;
  }

  @media screen and (max-width: breakpoint-sm-550) {
    padding: 40px 16px;
    gap: 30px;
  }
}

.main_title>h2 {

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  /* 56px */
  letter-spacing: -0.8px;

  @media screen and (max-width: breakpoint-sm-550) {
    font-size: 28px;
  }
}

.inner_container {
  display: flex;
  gap: 20px;
  max-width: 1192px;

  @media screen and (max-width: 1082px) {
    flex-direction: column;
  }
}

.tab_boxes {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  max-width: 384px;
  align-items: center;
  justify-content: center;

  @media screen and (max-width: 1082px) {
    max-width: fit-content;
    justify-content: center;
  }
}

.single_image {
  position: relative;
  cursor: pointer;
  height: 122px;
  width: 182px;
}

.single_image_selected {
  position: relative;
  cursor: pointer;
  background: white;
  border-radius: 8px;
  z-index: 1;
  height: 122px;
  width: 182px;
}

.single_image_selected::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
  z-index: -1;
  border-radius: 8px;
  pointer-events: none;
}

.imageWrapper {
  border-radius: 6px;
}

.titleWrapper {
  position: absolute;
  bottom: 4px;
  padding: 10px;
  min-height: 71px;
}

.imageTitle {

  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 160%;
}

.imageTitle_selected {
  color: colorWhite;

  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 160%;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  pointer-events: none;
  border-radius: 6px;
}

.right_container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
  max-width: 788px;

  @media screen and (max-width: 560px) {
    padding-top: 20px;
  }
}

.right_box {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.right_title>h3 {

  font-size: 21px;
  font-style: normal;
  font-weight: 600;
  line-height: 128%;
  letter-spacing: 0.42px;
  color: colorBlack;
}

.right_richText {

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 144%;
  color: colorBlack;
}

.tab_retail_section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;

  margin: 80px 32px;
}

.retail_title>h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 140%;
  letter-spacing: 0.8px;

  @media screen and (max-width: breakpoint-sm-450) {
    font-size: 28px;
    line-height: 138%;
  }
}

.retail_description>h3 {
  font-weight: 400;
  font-size: 20px;
  line-height: 160%;
  text-align: center;

  @media screen and (max-width: breakpoint-sm-450) {
    font-size: 18px;
    line-height: 168%;
  }
}

.embla__viewport {
  overflow: hidden;
}

.retail_container,
.embla__container {
  max-width: 90vw;
  display: flex;
  gap: 24px;
}

.tab_retail_box {
  position: relative;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  width: 283px;
  height: 108px;
  padding: 24px;
  border: 2px solid transparent;
  background-color: gray300;
  border-radius: 8px;
  cursor: pointer;

  font-weight: 600;
  font-size: 20px;
  line-height: 148%;
}

.tab_retail_box_selected {
  color: colorWhite;
  background-image: linear-gradient(colorBlack, colorBlack),
    linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;
}


.container_cloud {
  padding: 80px 32px;
  background-color: gray300;
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: center;


  @media screen and (max-width: breakpoint-md) {
    padding: 40px 32px;
  }

  @media screen and (max-width: breakpoint-sm) {
    padding: 40px 16px;
  }
}

.content_cloud {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.title_cloud h2 {
  color: colorBlack;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
  text-align: center;
}

.description_cloud {
  color: colorBlack;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  text-align: center;
}

.inner_container_cloud {
  display: flex;
  max-width: 1192px;
  gap: inherit;

  @media screen and (max-width: breakpoint-md) {
    flex-direction: column;
    gap: 30px;
  }

}

.tab_boxes_cloud {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 350px;
  overflow-y: auto;

  @media screen and (max-width: breakpoint-sm) {
    width: auto;
  }
}

.tab_boxes_cloud::-webkit-scrollbar {
  width: 3px;
}

.tab_boxes_cloud::-webkit-scrollbar-thumb {
  background-color: grayBorder;
  border-radius: 6px;
}

.single_box_cloud {
  padding: 20px 20px 20px 0;
  border-bottom: 2px solid grayBorder;
  cursor: pointer;

  font-size: 22px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  color: colorBlack;
}

.single_box_selected_cloud {
  padding: 20px 0;
  color: brandColorThree;
  border-bottom: 2px solid brandColorThree;
  cursor: pointer;

  font-size: 22px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
}

.right_container_cloud {
  padding: 20px;
  background-color: colorWhite;
  border-radius: 12px;
  max-width: 620px;
  height: fit-content;
}

.right_box_cloud {

  font-size: 22px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  color: colorBlack;
}