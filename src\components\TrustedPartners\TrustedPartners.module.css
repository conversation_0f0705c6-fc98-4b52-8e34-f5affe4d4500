@value variables: "@styles/variables.module.css";
@value colorBlack,colorWhite,fifteenSpace,grayBorder,brandColorOne,brandColorTwo,brandColorThree,brandColorFour,brandColorFive,twentyFiveSpace from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm from breakpoints;

.heading {
  font-weight: 600;
  font-size: 32px;
  line-height: 42.24px;
  letter-spacing: -0.32px;
  text-align: center;

  @media screen and (max-width: breakpoint-sm) {
    font-size: 22px;
    line-height: 30.36px;
    letter-spacing: 0;
  }
}

.trustedPartnersWrapper {
  background-color: colorBlack;
  color: colorWhite;
  width: 100%;
  max-height: twentyFiveSpace;
  height: twentyFiveSpace;
  padding-top: 40px;
  padding-bottom: 40px;
}
.logoWrapper {
  padding-top: 40px;
}
.logo {
  margin: 0 12px;
}
.embla {
  max-width: 80%;
  margin: auto;
  --slide-height: 5rem;
  --slide-spacing: 10rem;
  --slide-size: 10%;
  position: relative;
  pointer-events: none;
}
.embla::before,
.embla::after {
  content: '';
  position: absolute;

  bottom: 0;
  height: 60px;
  width: 60px; /* Adjust width as needed */
  background: linear-gradient(to left, transparent, rgba(0, 0, 0, 1));
  z-index: 1;
}

.embla::before {
  left: 0;
}

.embla::after {
  right: 0;
  background: linear-gradient(to right, transparent, rgba(0, 0, 0, 1));
}
.embla__viewport {
  overflow: hidden;
}
.embla__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}
.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
  display: flex;
  justify-content: center;
}
.embla__slide__number {
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--slide-height);
}
.embla__controls {
  display: grid;
  grid-template-columns: auto 1fr;
  justify-content: space-between;
  gap: 1.2rem;
  margin-top: 1.8rem;
}
.embla__buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.6rem;
  align-items: center;
}
.embla__button {
  -webkit-tap-highlight-color: rgba(var(--text-high-contrast-rgb-value), 0.5);
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  touch-action: manipulation;
  display: inline-flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0;
  box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
  width: 3.6rem;
  height: 3.6rem;
  z-index: 1;
  border-radius: 50%;
  color: var(--text-body);
  display: flex;
  align-items: center;
  justify-content: center;
}
.embla__button:disabled {
  color: var(--detail-high-contrast);
}
.embla__button__svg {
  width: 35%;
  height: 35%;
}
.embla__play {
  -webkit-tap-highlight-color: rgba(var(--text-high-contrast-rgb-value), 0.5);
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  touch-action: manipulation;
  display: inline-flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0;
  box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
  border-radius: 1.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  justify-self: flex-end;
  color: var(--text-body);
  font-weight: 700;
  font-size: 1.4rem;
  padding: 0 2.4rem;
  min-width: 8.4rem;
}
