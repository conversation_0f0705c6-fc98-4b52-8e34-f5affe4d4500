export interface TrustedPartnerSlideTypes {
  attributes: {
    alternativeText: string;
    caption: string;
    createdAt: string;
    ext: string;
    formats: null;
    hash: string;
    height: number;
    mime: string;
    name: string;
    previewUrl: null;
    provider: string;
    provider_metadata: null;
    size: number;
    updatedAt: string;
    url: string;
    width: number;
  };
}

export interface TrustedPartnersTypes {
  data: {
    id: number;
    title: string;
    partnersLogo: {
      id: number;
      images: {
        data: TrustedPartnerSlideTypes[];
      };
    };
  };
}
