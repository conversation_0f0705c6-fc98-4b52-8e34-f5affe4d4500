'use client';

import { Container } from 'react-bootstrap';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';
import styles from './VideoListing.module.css';
import Button from '@components/Button';
import Heading from '@components/Heading';
import useMediaQueryState from '@hooks/useMediaQueryState';
import breakpoints from '@styles/breakpoints.module.css';
import ReactPaginate from 'react-paginate';
import VideoModal from '@components/VideoModal';
import { useSearchParams } from 'next/navigation';

export default function VideoListing({
  filterdata,
  boxData,
  dropDownVideoTagsData,
}: {
  filterdata: any;
  boxData: any;
  dropDownVideoTagsData: any;
}) {
  const [show, setShow] = useState(false);
  const [showClose, setShowClose] = useState(false);
  const [videoLink, setVideoLink] = useState('');
  const [openDropdown, setOpenDropdown] = useState<'video tag' | null>(null);
  const searchParams = useSearchParams();
  const [selectedVideoTags, setSelectedVideoTags] = useState<string[]>(
    searchParams.get('filter') === 'client_testimonial'
      ? ['Client Testimonial']
      : [],
  );

  const [sorted, setSorted] = useState('desc');
  const [videos, setVideos] = useState(boxData);

  ///// pagination ///////////

  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 6;

  const handlePageChange = selectedPage => {
    setCurrentPage(selectedPage.selected); // ReactPaginate gives selected as object {selected: pageNumber}
  };

  // filteration will only happen if tags are changed in dropdown, otherwise the value memoized
  const filteredData =
    videos?.filter(data => {
      const videoTagsTitles =
        data?.attributes?.global_video_tag?.data?.attributes?.tag;

      const hasMatchingVideoTags =
        selectedVideoTags.length === 0 ||
        selectedVideoTags.some(selected => videoTagsTitles?.includes(selected));

      return hasMatchingVideoTags;
    }) || [];

  const pageCount = Math.ceil(filteredData?.length / itemsPerPage);
  const indexOfLastItem = (currentPage + 1) * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const paginatedData = filteredData?.slice(indexOfFirstItem, indexOfLastItem); // Paginate the filtered data

  ////////////////////////////////////////////////////

  // Function to remove selected value
  const removeSelectedValue = value => {
    setSelectedVideoTags(
      selectedVideoTags.filter(videoTag => videoTag !== value),
    );
  };

  const toggleDropdownVideoTag = () => {
    setOpenDropdown(prev => (prev === 'video tag' ? null : 'video tag'));
  };

  // Handler for selecting/unselecting services
  const handleVideoTagChange = (videoTitle: string) => {
    setSelectedVideoTags(prevSelectedVideoTags => {
      if (prevSelectedVideoTags.includes(videoTitle)) {
        return prevSelectedVideoTags.filter(
          videoTag => videoTag !== videoTitle,
        );
      } else {
        return [...prevSelectedVideoTags, videoTitle];
      }
    });
  };

  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-md-820']})`,
  });

  // State to control the visibility of the filter container
  const [isFilterVisible, setIsFilterVisible] = useState(false);

  // Function to toggle visibility
  const toggleFilterVisibility = () => {
    setIsFilterVisible(!isFilterVisible); // Toggle visibility
  };

  const handleSort = () => {
    const newVideos = [...videos];
    if (sorted === 'desc') {
      newVideos.sort(
        (video1, video2) =>
          new Date(video1.attributes?.publication_date).getTime() -
          new Date(video2.attributes?.publication_date).getTime(),
      );
      setSorted('asc');
    } else {
      newVideos.sort(
        (video1, video2) =>
          new Date(video2.attributes?.publication_date).getTime() -
          new Date(video1.attributes?.publication_date).getTime(),
      );
      setSorted('desc');
    }
    setVideos(newVideos);
  };

  // Dropdown refs
  const videoTagDropdownRef = useRef<HTMLDivElement>(null);
  // Close dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        videoTagDropdownRef.current &&
        !videoTagDropdownRef.current.contains(event.target as Node)
      ) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Reset currentPage to 0 when filter conditions change
  useEffect(() => {
    setCurrentPage(0);
  }, [selectedVideoTags]);

  return (
    <>
      {!isTablet ? (
        <>
          <Container fluid className={styles.filter_container}>
            <div className={styles.filter_content}>
              <Heading
                headingType="h2"
                title={filterdata?.all_videos?.title}
                className={styles.filter_title}
              />
            </div>
            <div className={styles.filter_tabs}>
              <div ref={videoTagDropdownRef} className={styles.dropdown_tabs}>
                <div
                  className={styles.dropdown_box}
                  onClick={toggleDropdownVideoTag}
                >
                  <div className={styles.dropdown_button}>
                    {filterdata?.all_videos?.title}
                  </div>
                  <div className={styles.arrow}>
                    <Image
                      src={
                        openDropdown === 'video tag'
                          ? 'https://cdn.marutitech.com/drop_down_upside_0c7e8734bc.svg'
                          : 'https://cdn.marutitech.com/drop_down_0839569e62.svg'
                      }
                      alt={'dropdown arrow'}
                      width={24}
                      height={24}
                    />
                  </div>
                </div>

                {openDropdown === 'video tag' && (
                  <div className={styles.dropdown_content}>
                    <ul>
                      {dropDownVideoTagsData?.map((videoTag: any) => (
                        <li key={videoTag.id}>
                          <label className={styles.checkbox_label}>
                            <input
                              type="checkbox"
                              checked={selectedVideoTags.includes(
                                videoTag?.attributes?.tag,
                              )}
                              onChange={() =>
                                handleVideoTagChange(videoTag?.attributes?.tag)
                              }
                            />
                            {videoTag?.attributes?.tag}
                          </label>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
              <div className={styles.filterBtnWrapper}>
                <button
                  className={styles.sortButton}
                  onClick={() => handleSort()}
                >
                  {`${filterdata?.all_videos?.sort_by_text} ${
                    sorted === 'desc' ? 'Newest' : 'Oldest'
                  } `}
                  <Image
                    height={24}
                    width={24}
                    alt="arrow down"
                    src="https://cdn.marutitech.com/arrow_down_ff219636f3.svg"
                    className={sorted === 'asc' ? styles.arrowImage : undefined}
                  />
                </button>
                <Button
                  className={styles.clear_button}
                  label={'Clear'}
                  type="submit"
                  onClick={() => {
                    setSelectedVideoTags([]);
                  }}
                />
              </div>
            </div>
            <div className={styles.selected_value_container}>
              {selectedVideoTags.map((value, index) => (
                <div className={styles.selected_value_box} key={index}>
                  {value}
                  <Image
                    src="https://cdn.marutitech.com/x_f7d037f941.svg"
                    alt="Remove"
                    width={24}
                    height={24}
                    className={styles.close_icon}
                    onClick={() => removeSelectedValue(value)}
                  />
                </div>
              ))}
            </div>
          </Container>
          <Container fluid className={styles.container}>
            <div className={styles.cardWrapper}>
              {filteredData.length > 0 ? (
                paginatedData.map(video => (
                  <div key={video.id} className={styles.videoCard}>
                    {video?.attributes?.global_video_tag?.data?.attributes
                      ?.tag && (
                      <div className={styles.tagName}>
                        {
                          video?.attributes?.global_video_tag?.data?.attributes
                            ?.tag
                        }
                      </div>
                    )}
                    <div
                      className={styles.videoContainer}
                      onClick={() => {
                        setShow(true);
                        setShowClose(true);
                        setVideoLink(video?.attributes?.url);
                      }}
                    >
                      <Image
                        src={
                          filterdata?.play_button_across_page?.data?.attributes
                            ?.url
                        }
                        width={65}
                        height={65}
                        alt="play button"
                        className={styles.playButton}
                      />
                      <Image
                        loading="eager"
                        priority
                        key={
                          video?.attributes?.thumbnail?.data?.attributes?.url
                        }
                        src={
                          video?.attributes?.thumbnail?.data?.attributes?.format
                            ?.small?.url ||
                          video?.attributes?.thumbnail?.data?.attributes
                            ?.formats?.small?.url ||
                          video?.attributes?.thumbnail?.data?.attributes?.format
                            ?.medium?.url ||
                          video?.attributes?.thumbnail?.data?.attributes
                            ?.formats?.medium?.url ||
                          video?.attributes?.thumbnail?.data?.attributes?.url
                        }
                        fill
                        alt={
                          video?.attributes?.thumbnail?.data?.attributes
                            ?.alternativeText
                        }
                        className={styles.thumbnail}
                      />
                    </div>
                    <Heading
                      headingType="h3"
                      title={video?.attributes?.title}
                      className={styles.card_title}
                    />
                  </div>
                ))
              ) : (
                <div className={styles.no_data_found}>No data found.</div>
              )}
            </div>

            {/* Pagination Controls using ReactPaginate */}
            {filteredData.length > 0 && (
              <div className={styles.paginationContainer}>
                <ReactPaginate
                  previousLabel={'<'}
                  nextLabel={'>'}
                  pageCount={pageCount}
                  onPageChange={handlePageChange}
                  forcePage={currentPage}
                  containerClassName={styles.pagination}
                  previousLinkClassName={styles.paginationLink}
                  nextLinkClassName={styles.paginationLink}
                  disabledClassName={styles.paginationDisabled}
                  activeClassName={styles.paginationActive}
                />
              </div>
            )}
          </Container>
          <VideoModal
            show={show}
            setShow={setShow}
            setShowClose={setShowClose}
            showClose={showClose}
            videoLink={videoLink}
          />
        </>
      ) : (
        <>
          <div className={styles.button_filter_mobile}>
            <Button
              className={styles.filter_button_mobile}
              label={'Filter'}
              type="submit"
              onClick={toggleFilterVisibility}
            />
            <button className={styles.sortButton} onClick={() => handleSort()}>
              {`${filterdata?.all_videos?.sort_by_text} ${
                sorted === 'desc' ? 'Newest' : 'Oldest'
              } `}
              <Image
                height={24}
                width={24}
                alt="arrow"
                src="https://cdn.marutitech.com/arrow_down_ff219636f3.svg"
                className={sorted === 'asc' ? styles.arrowImage : undefined}
              />
            </button>
          </div>

          {isFilterVisible && (
            <Container fluid className={styles.filter_container}>
              <Heading
                headingType="h2"
                title={filterdata?.all_videos?.title}
                className={styles.filter_title}
              />
              <div className={styles.filter_tabs}>
                <div ref={videoTagDropdownRef} className={styles.dropdown_tabs}>
                  <div
                    className={styles.dropdown_box}
                    onClick={toggleDropdownVideoTag}
                  >
                    <div className={styles.dropdown_button}>
                      {filterdata?.all_videos?.title}
                    </div>
                    <div className={styles.arrow}>
                      <Image
                        src={
                          openDropdown === 'video tag'
                            ? 'https://cdn.marutitech.com/drop_down_upside_0c7e8734bc.svg'
                            : 'https://cdn.marutitech.com/drop_down_0839569e62.svg'
                        }
                        alt={'dropdown arrow'}
                        width={24}
                        height={24}
                      />
                    </div>
                  </div>

                  {openDropdown === 'video tag' && (
                    <div className={styles.dropdown_content}>
                      <ul>
                        {dropDownVideoTagsData?.map((videoTag: any) => (
                          <li key={videoTag.id}>
                            <label className={styles.checkbox_label}>
                              <input
                                type="checkbox"
                                checked={selectedVideoTags.includes(
                                  videoTag?.attributes?.tag,
                                )}
                                onChange={() =>
                                  handleVideoTagChange(
                                    videoTag?.attributes?.tag,
                                  )
                                }
                              />
                              {videoTag?.attributes?.tag}
                            </label>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
              <div className={styles.selected_value_container}>
                {selectedVideoTags.map((value, index) => (
                  <div className={styles.selected_value_box} key={index}>
                    {value}
                    <Image
                      src="https://cdn.marutitech.com/x_f7d037f941.svg"
                      alt="Remove"
                      width={24}
                      height={24}
                      className={styles.close_icon}
                      onClick={() => removeSelectedValue(value)}
                    />
                  </div>
                ))}
              </div>
            </Container>
          )}
          <Container fluid className={styles.container}>
            <div className={styles.cardWrapper}>
              {filteredData.length > 0 ? (
                paginatedData.map(video => (
                  <div key={video.id} className={styles.videoCard}>
                    {video?.attributes?.global_video_tag?.data?.attributes
                      ?.tag && (
                      <div className={styles.tagName}>
                        {
                          video?.attributes?.global_video_tag?.data?.attributes
                            ?.tag
                        }
                      </div>
                    )}
                    <div
                      className={styles.videoContainer}
                      onClick={() => {
                        setShow(true);
                        setShowClose(true);
                        setVideoLink(video?.attributes?.url);
                      }}
                    >
                      <Image
                        src={
                          filterdata?.play_button_across_page?.data?.attributes
                            ?.url
                        }
                        width={65}
                        height={65}
                        alt="play button"
                        className={styles.playButton}
                      />
                      <Image
                        loading="eager"
                        priority
                        key={
                          video?.attributes?.thumbnail?.data?.attributes?.url
                        }
                        src={
                          video?.attributes?.thumbnail?.data?.attributes?.format
                            ?.small?.url ||
                          video?.attributes?.thumbnail?.data?.attributes
                            ?.formats?.small?.url ||
                          video?.attributes?.thumbnail?.data?.attributes?.format
                            ?.medium?.url ||
                          video?.attributes?.thumbnail?.data?.attributes
                            ?.formats?.medium?.url ||
                          video?.attributes?.thumbnail?.data?.attributes?.url
                        }
                        fill
                        alt={
                          video?.attributes?.thumbnail?.data?.attributes
                            ?.alternativeText
                        }
                        className={styles.thumbnail}
                      />
                    </div>
                    <Heading
                      headingType="h3"
                      title={video?.attributes?.title}
                      className={styles.card_title}
                    />
                  </div>
                ))
              ) : (
                <div className={styles.no_data_found}>No data found.</div>
              )}
            </div>

            {/* Pagination Controls using ReactPaginate */}
            {filteredData.length > 0 && (
              <div className={styles.paginationContainer}>
                <ReactPaginate
                  previousLabel={'<'}
                  nextLabel={'>'}
                  pageCount={pageCount}
                  onPageChange={handlePageChange}
                  forcePage={currentPage}
                  containerClassName={styles.pagination}
                  previousLinkClassName={styles.paginationLink}
                  nextLinkClassName={styles.paginationLink}
                  disabledClassName={styles.paginationDisabled}
                  activeClassName={styles.paginationActive}
                />
              </div>
            )}
          </Container>
          <VideoModal
            show={show}
            setShow={setShow}
            setShowClose={setShowClose}
            showClose={showClose}
            videoLink={videoLink}
          />
        </>
      )}
    </>
  );
}
