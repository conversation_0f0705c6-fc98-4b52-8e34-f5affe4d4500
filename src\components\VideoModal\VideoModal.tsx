'use client';

import React, { Dispatch, SetStateAction } from 'react';
import { Col, Container, Modal } from 'react-bootstrap';
import classNames from '@utils/classNames';

import styles from './VideoModal.module.css';

interface VideoModalProps {
  show?: boolean;
  setShow?: Dispatch<SetStateAction<boolean>>;
  setShowClose?: Dispatch<SetStateAction<boolean>>;
  showClose?: boolean;
  videoLink?: string;
}
export default function VideoModal({
  show,
  setShow,
  setShowClose,
  showClose,
  videoLink,
}: VideoModalProps) {
  return (
    <Modal
      show={show}
      size="xl"
      onHide={() => {
        setShowClose(false);
        setShow(false);
      }}
      centered
    >
      <Modal.Header
        closeButton={showClose}
        className={classNames(styles['modal-header'], styles['btn-close'])}
      />
      <Modal.Body>
        <Col className={styles.respcontainer}>
          <iframe
            className={styles.respiframe}
            src={`${videoLink}?autoplay=1&enablejsapi=1&rel=0`}
            title="YouTube video player"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            loading="lazy"
          />
        </Col>
      </Modal.Body>
    </Modal>
  );
}
