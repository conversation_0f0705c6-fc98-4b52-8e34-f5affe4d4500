import VisionMission from './VisionMission';

export default {
  title: 'Components/Vision Mission',
};

const data = {
  data: {
    id: 1,
    attributes: {
      createdAt: '2024-07-31T05:20:54.419Z',
      updatedAt: '2024-08-22T13:34:52.762Z',
      publishedAt: '2024-07-31T05:20:56.020Z',

      vision_mission: [
        {
          id: 1,
          title: 'Our Vision',
          description:
            '<p>Propel businesses towards unparalleled success with innovative digital engineering solutions that evolve alongside their needs.</p>',
          image: {
            data: {
              id: 73,
              attributes: {
                name: 'vison_mission.png',
                alternativeText: null,
                caption: null,
                width: 720,
                height: 500,
                formats: {
                  thumbnail: {
                    name: 'thumbnail_vison_mission.png',
                    hash: 'thumbnail_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 225,
                    height: 156,
                    size: 88.94,
                    sizeInBytes: 88935,
                    url: 'https://cdn.marutitech.com/thumbnail_vison_mission_0bc7851090.png',
                  },
                  small: {
                    name: 'small_vison_mission.png',
                    hash: 'small_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 500,
                    height: 347,
                    size: 399.84,
                    sizeInBytes: 399842,
                    url: 'https://cdn.marutitech.com/small_vison_mission_0bc7851090.png',
                  },
                },
                hash: 'vison_mission_0bc7851090',
                ext: '.png',
                mime: 'image/png',
                size: 135.39,
                url: 'https://cdn.marutitech.com/vison_mission_0bc7851090.png',
                previewUrl: null,
                provider:
                  '@strapi-community/strapi-provider-upload-google-cloud-storage',
                provider_metadata: null,
                createdAt: '2024-08-08T12:23:07.446Z',
                updatedAt: '2024-08-08T12:23:07.446Z',
              },
            },
          },
        },
        {
          id: 2,
          title: 'Our Mission',
          description:
            '<p>Through our deep domain expertise &amp; passion for innovation, we engineer our customers’ end-to-end digital journey in order to help them realize desired business outcomes.</p>',
          image: {
            data: {
              id: 73,
              attributes: {
                name: 'vison_mission.png',
                alternativeText: null,
                caption: null,
                width: 720,
                height: 500,
                formats: {
                  thumbnail: {
                    name: 'thumbnail_vison_mission.png',
                    hash: 'thumbnail_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 225,
                    height: 156,
                    size: 88.94,
                    sizeInBytes: 88935,
                    url: 'https://cdn.marutitech.com/thumbnail_vison_mission_0bc7851090.png',
                  },
                  small: {
                    name: 'small_vison_mission.png',
                    hash: 'small_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 500,
                    height: 347,
                    size: 399.84,
                    sizeInBytes: 399842,
                    url: 'https://cdn.marutitech.com/small_vison_mission_0bc7851090.png',
                  },
                },
                hash: 'vison_mission_0bc7851090',
                ext: '.png',
                mime: 'image/png',
                size: 135.39,
                url: 'https://cdn.marutitech.com/vison_mission_0bc7851090.png',
                previewUrl: null,
                provider:
                  '@strapi-community/strapi-provider-upload-google-cloud-storage',
                provider_metadata: null,
                createdAt: '2024-08-08T12:23:07.446Z',
                updatedAt: '2024-08-08T12:23:07.446Z',
              },
            },
          },
        },
      ],
    },
    meta: {},
  },
};

export function VisionMissionStory() {
  return (
    <VisionMission visionMissionData={data?.data?.attributes?.vision_mission} />
  );
}
