@value variables: "@styles/variables.module.css";
@value breakPoints: "@styles/breakpoints.module.css";
@value fiveSpace,twoSpace from variables;
@value breakpoint-sm-480, breakpoint-md from breakPoints;

.grid {
  display: grid;
  /* autoprefixer: off */
  grid-template-rows: auto;
}

.columns1 {
  grid-template-columns: repeat(1, 1fr);
  /* autoprefixer: off */
  grid-gap: fiveSpace;
}

.columns2 {
  grid-template-columns: repeat(2, 1fr);
  /* autoprefixer: off */
  grid-gap: fiveSpace;

  @media (max-width: breakpoint-sm-480) {
    grid-template-columns: repeat(1, 1fr);
    /* autoprefixer: off */
    grid-gap: twoSpace;
  }
}

.columns3 {
  grid-template-columns: repeat(3, 1fr);
  /* autoprefixer: off */
  grid-gap: fiveSpace;

  @media (max-width: breakpoint-md) {
    grid-template-columns: repeat(2, 1fr);
    /* autoprefixer: off */
    grid-gap: twoSpace;
  }

  @media (max-width: breakpoint-sm-480) {
    grid-template-columns: repeat(1, 1fr);
    /* autoprefixer: off */
    grid-gap: fiveSpace;
  }
}

.columns4 {
  grid-template-columns: repeat(4, 1fr);
  /* autoprefixer: off */
  grid-gap: fiveSpace;

  @media (max-width: breakpoint-md) {
    grid-template-columns: repeat(2, 1fr);
    /* autoprefixer: off */
    grid-gap: twoSpace;
  }

  @media (max-width: breakpoint-sm-480) {
    grid-template-columns: repeat(1, 1fr);
    /* autoprefixer: off */
    grid-gap: fiveSpace;
  }
}

.columns8 {
  grid-template-columns: repeat(8, 1fr);
  /* autoprefixer: off */
  grid-gap: fiveSpace;

  @media (max-width: largeWidth) {
    grid-template-columns: repeat(6, 1fr);
    /* autoprefixer: off */
    grid-gap: twoSpace;
  }

  @media (max-width: breakpoint-md) {
    grid-template-columns: repeat(4, 1fr);
    /* autoprefixer: off */
    grid-gap: twoSpace;
  }

  @media (max-width: breakpoint-sm-480) {
    grid-template-columns: repeat(1, 1fr);
    /* autoprefixer: off */
    grid-gap: fiveSpace;
  }
}
