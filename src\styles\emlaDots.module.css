@value variables: "@styles/variables.module.css";
@value colorBlack,colorWhite,brandColorOne,brandColorTwo,brandColorThree,brandColorFour,brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-md-850 from breakpoints;

.embla__controls {
  display: flex;
  justify-content: center;
  padding-top: 40px;
}

.embla__dots {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  margin-right: calc((2.6rem - 1.4rem) / 2 * -1);
  outline: none;
  border: 0;

  @media screen and (max-width: breakpoint-md-850) {
    justify-content: center;
  }
}
.embla__dot {
  padding: 0 !important;
  background: colorWhite;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  cursor: pointer;
  width: 11px;
  height: 11px;
  margin: 0 3.5px;
  border: 0;
  border-radius: 50%;
  outline: none;
  transition: 0.5s width;
}
.embla__dot_bg_white {
  background: #d9d9d9;
}
.embla__dot_selected {
  width: 26px;
  height: 12px;
  border: 0;
  outline: none;
  border-radius: 20px;
  background: linear-gradient(
    93.12deg,
    brandColorOne 0%,
    brandColorTwo 30.56%,
    brandColorThree 53.47%,
    brandColorFour 75.75%,
    brandColorFive 100%
  );
}
