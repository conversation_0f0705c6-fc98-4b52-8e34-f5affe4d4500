@value variables: "./variables.module.css";
@value h1FontSize, h1MobileFontSize, h2FontSize, h2MobileFontSize, h3FontSize, h3MobileFontSize, h4FontSize, h4MobileFontSize, h5FontSize, h5MobileFontSize, h6FontSize, h6MobileFontSize, fontWeight600, fontWeight700 from variables;

@value breakPoints: "./breakpoints.module.css";
@value breakpoint-sm-450 from breakPoints;

.h1 {
  font-size: h1FontSize;

  font-weight: fontWeight700;

  @media (max-width: breakpoint-sm-450) {
    font-size: h1MobileFontSize;
  }
}

.h2 {
  font-size: h2FontSize;

  @media (max-width: breakpoint-sm-450) {
    font-size: h2MobileFontSize;
  }
}

.h3 {
  font-size: h3FontSize;

  @media (max-width: breakpoint-sm-450) {
    font-size: h3MobileFontSize;
  }
}

.h4 {
  font-size: h4FontSize;
  font-weight: 600;

  @media (max-width: breakpoint-sm-450) {
    font-size: h4MobileFontSize;
  }
}

.h5 {
  font-size: h5FontSize;
  font-weight: fontWeight600;

  @media (max-width: breakpoint-sm-450) {
    font-size: h5MobileFontSize;
  }
}

.h6 {
  font-size: h6FontSize;
  font-weight: 600;

  @media (max-width: breakpoint-sm-450) {
    font-size: h6MobileFontSize;
  }
}

.caption {
  font-size: 12px;
  line-height: 1.67;
  font-weight: normal;
  letter-spacing: 0.01em;
  max-width: 50em;
}