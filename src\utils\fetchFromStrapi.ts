export default async function fetchFromStrapi(endpoint, queryString = '') {
  const baseUrl = process.env.NEXT_PUBLIC_STRAPI_URL;

  try {
    const url = `${baseUrl}/api/${endpoint}${queryString ? `?${queryString}` : ''}`;

    console.log('strapi request url:', url);

    const response = await fetch(url, {
      method: 'GET',
      cache: 'force-cache',
    });

    if (!response.ok) {
      throw new Error(
        `Strapi fetch error: ${response.status} - ${response.statusText}`,
      );
    }

    return await response.json();
  } catch (error) {
    console.error(`Failed to fetch ${endpoint}:`, error);
    return null;
  }
}
