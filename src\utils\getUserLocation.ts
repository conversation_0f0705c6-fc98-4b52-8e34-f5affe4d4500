import { useEffect, useState } from 'react';
import getUserIPData from 'common/getUserIpData';

export default function getUserLocation() {
  const [userCountryCode, setUserCountryCode] = useState<string>('');

  useEffect(() => {
    const fetchIpData = async () => {
      try {
        const data = await getUserIPData();
        setUserCountryCode(data?.location?.country_code?.toLowerCase() || '');
      } catch (error) {
        setUserCountryCode('us');
        // Silently handle error of user IP data
      }
    };

    fetchIpData();
  }, []);

  return userCountryCode;
}
