import fetchFromStrapi from './fetchFromStrapi';

export interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?:
    | 'always'
    | 'hourly'
    | 'daily'
    | 'weekly'
    | 'monthly'
    | 'yearly'
    | 'never';
  priority?: number;
}

export interface SitemapCategory {
  name: string;
  urls: SitemapUrl[];
}

// Static pages configuration
const STATIC_PAGES = [
  { path: '/', priority: 1.0, changefreq: 'weekly' as const },
  { path: '/about-us', priority: 0.8, changefreq: 'monthly' as const },
  { path: '/services', priority: 0.9, changefreq: 'weekly' as const },
  { path: '/careers', priority: 0.7, changefreq: 'weekly' as const },
  { path: '/contact-us', priority: 0.8, changefreq: 'monthly' as const },
  { path: '/resources', priority: 0.8, changefreq: 'daily' as const },
  { path: '/case-study', priority: 0.8, changefreq: 'weekly' as const },
  { path: '/blog', priority: 0.8, changefreq: 'daily' as const },
  { path: '/events', priority: 0.7, changefreq: 'weekly' as const },
  { path: '/ebooks', priority: 0.7, changefreq: 'weekly' as const },
  { path: '/podcasts', priority: 0.7, changefreq: 'weekly' as const },
  { path: '/videos', priority: 0.7, changefreq: 'weekly' as const },
  { path: '/search', priority: 0.5, changefreq: 'monthly' as const },
  { path: '/thank-you', priority: 0.3, changefreq: 'yearly' as const },
  { path: '/privacy-policy', priority: 0.4, changefreq: 'yearly' as const },
  { path: '/cookie-policy', priority: 0.4, changefreq: 'yearly' as const },
  {
    path: '/ai-readiness-audit',
    priority: 0.8,
    changefreq: 'monthly' as const,
  },
];

/**
 * Get the base URL for the site
 */
export function getBaseUrl(): string {
  return process.env.NEXT_PUBLIC_SITE_URL || 'https://dev.marutitech.com';
}

/**
 * Generate lastmod date in ISO format
 */
export function generateLastMod(date?: Date): string {
  return (date || new Date()).toISOString();
}

/**
 * Fetch dynamic routes from Strapi
 */
export async function fetchDynamicRoutes(): Promise<SitemapUrl[]> {
  const routes: SitemapUrl[] = [];
  const baseUrl = getBaseUrl();

  try {
    // Fetch all dynamic route data in parallel
    const [
      servicesL2,
      servicesL3,
      cityServices,
      industries,
      partners,
      awsSolutions,
      cloudConsulting,
      retail,
      blogs,
      caseStudies,
      events,
      ebooks,
    ] = await Promise.all([
      fetchFromStrapi('l2-service-pages'),
      fetchFromStrapi('l3-services-pages', 'populate=l_2_service_page'),
      fetchFromStrapi('city-service-pages'),
      fetchFromStrapi('industries'),
      fetchFromStrapi('partners'),
      fetchFromStrapi('solutions'),
      fetchFromStrapi('cloud-consultings'),
      fetchFromStrapi('retail-pages'),
      fetchFromStrapi('blogs'),
      fetchFromStrapi('case-studies'),
      fetchFromStrapi('event-main-pages'),
      fetchFromStrapi('e-books'),
    ]);

    console.log('servicesL3', cityServices?.data[0]);

    // L2 Services (/services/[service])
    if (servicesL2?.data) {
      servicesL2.data.forEach((item: any) => {
        routes.push({
          loc: `${baseUrl}/services/${item.attributes.slug}`,
          lastmod: generateLastMod(new Date(item.attributes.updatedAt)),
          changefreq: 'weekly',
          priority: 0.8,
        });
      });
    }

    // L3 Services (/services/[service]/[l3service])
    if (servicesL3?.data) {
      servicesL3.data.forEach((item: any) => {
        const l2Slug = item.attributes.l_2_service_page?.data?.attributes?.slug;
        if (l2Slug) {
          routes.push({
            loc: `${baseUrl}/services/${l2Slug}/${item.attributes.slug}`,
            lastmod: generateLastMod(new Date(item.attributes.updatedAt)),
            changefreq: 'weekly',
            priority: 0.7,
          });
        }
      });
    }

    // City Services (/service/[service])
    if (cityServices?.data) {
      cityServices.data.forEach((item: any) => {
        routes.push({
          loc: `${baseUrl}/service/${item.attributes.slug}`,
          lastmod: generateLastMod(new Date(item.attributes.updatedAt)),
          changefreq: 'weekly',
          priority: 0.7,
        });
      });
    }

    // Industries (/industry/[industry])
    if (industries?.data) {
      industries.data.forEach((item: any) => {
        routes.push({
          loc: `${baseUrl}/industry/${item.attributes.slug}`,
          lastmod: generateLastMod(new Date(item.attributes.updatedAt)),
          changefreq: 'weekly',
          priority: 0.8,
        });
      });
    }

    // Partners (/partners/[partnerSlug])
    if (partners?.data) {
      partners.data.forEach((item: any) => {
        routes.push({
          loc: `${baseUrl}/partners/${item.attributes.slug}`,
          lastmod: generateLastMod(new Date(item.attributes.updatedAt)),
          changefreq: 'monthly',
          priority: 0.6,
        });
      });
    }

    // AWS Solutions (/partners/aws/[solutionSlug])
    if (awsSolutions?.data) {
      awsSolutions.data.forEach((item: any) => {
        routes.push({
          loc: `${baseUrl}/partners/aws/${item.attributes.slug}`,
          lastmod: generateLastMod(new Date(item.attributes.updatedAt)),
          changefreq: 'monthly',
          priority: 0.6,
        });
      });
    }

    // Cloud Consulting (/cloud-consulting/[cloud])
    if (cloudConsulting?.data) {
      cloudConsulting.data.forEach((item: any) => {
        routes.push({
          loc: `${baseUrl}/cloud-consulting/${item.attributes.slug}`,
          lastmod: generateLastMod(new Date(item.attributes.updatedAt)),
          changefreq: 'weekly',
          priority: 0.7,
        });
      });
    }

    // Retail (/retail/[retail])
    if (retail?.data) {
      retail.data.forEach((item: any) => {
        routes.push({
          loc: `${baseUrl}/retail/${item.attributes.slug}`,
          lastmod: generateLastMod(new Date(item.attributes.updatedAt)),
          changefreq: 'weekly',
          priority: 0.7,
        });
      });
    }

    console.log('blogs data:', blogs?.data[0]);

    // Blogs (/blog/[blogDetails])
    if (blogs?.data) {
      blogs.data.forEach((item: any) => {
        routes.push({
          loc: `${baseUrl}/blog/${item.attributes.slug}`,
          lastmod: generateLastMod(new Date(item.attributes.updatedAt)),
          changefreq: 'monthly',
          priority: 0.6,
        });
      });
    }

    // Case Studies (/case-study/[caseStudy])
    if (caseStudies?.data) {
      caseStudies.data.forEach((item: any) => {
        routes.push({
          loc: `${baseUrl}/case-study/${item.attributes.slug}`,
          lastmod: generateLastMod(new Date(item.attributes.updatedAt)),
          changefreq: 'monthly',
          priority: 0.7,
        });
      });
    }

    // Events (/events/[eventSlug])
    if (events?.data) {
      events.data.forEach((item: any) => {
        routes.push({
          loc: `${baseUrl}/events/${item.attributes.slug}`,
          lastmod: generateLastMod(new Date(item.attributes.updatedAt)),
          changefreq: 'weekly',
          priority: 0.6,
        });
      });
    }

    // E-books (/ebooks/[ebooks])
    if (ebooks?.data) {
      ebooks.data.forEach((item: any) => {
        routes.push({
          loc: `${baseUrl}/ebooks/${item.attributes.slug}`,
          lastmod: generateLastMod(new Date(item.attributes.updatedAt)),
          changefreq: 'monthly',
          priority: 0.6,
        });
      });
    }
  } catch (error) {
    console.error('Error fetching dynamic routes for sitemap:', error);
  }

  return routes;
}

/**
 * Get static pages as sitemap URLs
 */
export function getStaticPages(): SitemapUrl[] {
  const baseUrl = getBaseUrl();
  const currentDate = generateLastMod();

  return STATIC_PAGES.map(page => ({
    loc: `${baseUrl}${page.path}`,
    lastmod: currentDate,
    changefreq: page.changefreq,
    priority: page.priority,
  }));
}

/**
 * Generate all sitemap URLs (static + dynamic)
 */
export async function generateAllSitemapUrls(): Promise<SitemapUrl[]> {
  const staticPages = getStaticPages();
  const dynamicPages = await fetchDynamicRoutes();

  return [...staticPages, ...dynamicPages];
}

/**
 * Categorize URLs for human-readable sitemap
 */
export async function categorizeSitemapUrls(): Promise<SitemapCategory[]> {
  const allUrls = await generateAllSitemapUrls();
  const baseUrl = getBaseUrl();

  const categories: SitemapCategory[] = [
    { name: 'Main Pages', urls: [] },
    { name: 'Services', urls: [] },
    { name: 'Industries', urls: [] },
    { name: 'Partners', urls: [] },
    { name: 'Resources', urls: [] },
    { name: 'Case Studies', urls: [] },
    { name: 'Blog Posts', urls: [] },
    { name: 'Events', urls: [] },
    { name: 'E-books', urls: [] },
    { name: 'Other Pages', urls: [] },
  ];

  allUrls.forEach(url => {
    const path = url.loc.replace(baseUrl, '');

    console.log('path', path);

    if (
      path === '/' ||
      path === '/about-us' ||
      path === '/contact-us' ||
      path === '/careers'
    ) {
      categories[0].urls.push(url);
    } else if (path.startsWith('/services') || path.startsWith('/service')) {
      categories[1].urls.push(url);
    } else if (path.startsWith('/industry')) {
      categories[2].urls.push(url);
    } else if (path.startsWith('/partners')) {
      categories[3].urls.push(url);
    } else if (
      path.startsWith('/resources') ||
      path.startsWith('/videos') ||
      path.startsWith('/podcasts')
    ) {
      categories[4].urls.push(url);
    } else if (path.startsWith('/case-study')) {
      categories[5].urls.push(url);
    } else if (path.startsWith('/blog')) {
      categories[6].urls.push(url);
    } else if (path.startsWith('/events')) {
      categories[7].urls.push(url);
    } else if (path.startsWith('/ebooks')) {
      categories[8].urls.push(url);
    } else {
      categories[9].urls.push(url);
    }
  });

  // Sort URLs within each category
  categories.forEach(category => {
    category.urls.sort((a, b) => a.loc.localeCompare(b.loc));
  });

  // Filter out empty categories
  return categories.filter(category => category.urls.length > 0);
}

/**
 * Generate XML sitemap string
 */
export function generateXmlSitemap(urls: SitemapUrl[]): string {
  const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>';
  const urlsetOpen =
    '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
  const urlsetClose = '</urlset>';

  const urlEntries = urls
    .map(url => {
      let entry = `  <url>\n    <loc>${url.loc}</loc>`;

      if (url.lastmod) {
        entry += `\n    <lastmod>${url.lastmod}</lastmod>`;
      }

      if (url.changefreq) {
        entry += `\n    <changefreq>${url.changefreq}</changefreq>`;
      }

      if (url.priority !== undefined) {
        entry += `\n    <priority>${url.priority}</priority>`;
      }

      entry += '\n  </url>';
      return entry;
    })
    .join('\n');

  return `${xmlHeader}\n${urlsetOpen}\n${urlEntries}\n${urlsetClose}`;
}
