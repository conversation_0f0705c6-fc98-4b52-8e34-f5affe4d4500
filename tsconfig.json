{
  "include": [
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    ".next"
  ],
  "compilerOptions": {
    "incremental": true,
    "allowJs": true,
    "checkJs": true,
    "baseUrl": "./src",
    "jsx": "preserve",
    "paths": {
      "@components/*": [
        "components/*"
      ],
      "@hooks/*": [
        "hooks/*"
      ],
      "@constants/*": [
        "constants/*"
      ],
      "@utils/*": [
        "utils/*"
      ],
      "@stories/*": [
        "stories/*"
      ],
      "@styles/*": [
        "styles/*"
      ],
      "@contexts/*": [
        "contexts/*"
      ],
      "@public/*": [
        "public/*"
      ],
    },
    "module": "esnext",
    "target": "esnext",
    "lib": [
      "dom"
    ],
    "declaration": true,
    "declarationMap": true,
    "strict": false,
    "moduleResolution": "node",
    "skipLibCheck": true,
    "esModuleInterop": true,
    "noEmit": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "outDir": "./next",
    "plugins": [
      {
        "name": "next"
      }
    ]
  }
}
